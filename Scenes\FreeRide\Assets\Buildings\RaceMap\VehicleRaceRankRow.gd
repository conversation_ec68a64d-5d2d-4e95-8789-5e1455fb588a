extends Panel

@onready var rank_label = $RankLabel
@onready var name_label = $NameLabel
@onready var me_panel = $MePanel

@onready var smart_label: Label = $HBoxContainer/SmartLabel
@onready var smart_texture: TextureRect = $HBoxContainer/SmartTexture
@onready var coin_label: Label = $HBoxContainer/CoinLabel
@onready var coin_texture: TextureRect = $HBoxContainer/CoinTexture
@onready var time_label: Label = $TimeLabel


var data

func set_data(_data):
	data = _data
	rank_label.text = str(data["rank"])
	name_label.text = data["handle"]
	
	time_label.text = str(data["time"] / 1000.0)
	coin_label.text = str(data["coin"])
	smart_label.text = str(data["smart"])
	
	#if data["id"] == DataSaver.get_item("id"):
		#me_panel.visible = true
	#else:
		#me_panel.visible = false
