[gd_scene load_steps=9 format=3 uid="uid://bwfs8gdvcu71i"]

[ext_resource type="Script" path="res://Scenes/FreeRide/Assets/Buildings/Police/assets/PoliceCallPicker.gd" id="1_v6vgu"]
[ext_resource type="PackedScene" uid="uid://cixw0i4rjg3rx" path="res://Scenes/ui/ExitableControl.tscn" id="2_fkvre"]
[ext_resource type="StyleBox" uid="uid://3jmm6a0rtg6b" path="res://Scenes/ui/panel_background.tres" id="3_6gxwk"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="4_fo8s3"]
[ext_resource type="Theme" uid="uid://cavx3qitdfs8s" path="res://Scenes/ui/ui_theme.tres" id="5_t3bc4"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="6_v0g5y"]
[ext_resource type="Texture2D" uid="uid://bw3w61kw7wpki" path="res://Scenes/ui/assets/PNG/grey_crossWhite.png" id="7_b04qv"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="8_f0qrt"]

[node name="PoliceCallPicker" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_v6vgu")

[node name="HUD" parent="." instance=ExtResource("2_fkvre")]
layout_mode = 1

[node name="Panel" type="Panel" parent="HUD"]
custom_minimum_size = Vector2(600, 600)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -300.0
offset_right = 300.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = ExtResource("3_6gxwk")

[node name="Title" type="Label" parent="HUD/Panel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -31.5
offset_right = 31.5
offset_bottom = 48.0
grow_horizontal = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_fo8s3")
theme_override_font_sizes/font_size = 30
text = "CHOOSE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ScrollContainer" type="ScrollContainer" parent="HUD/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 11.0
offset_top = 49.0
offset_right = -8.0
offset_bottom = -13.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("5_t3bc4")
scroll_deadzone = 50

[node name="VBoxContainer" type="VBoxContainer" parent="HUD/Panel/ScrollContainer"]
layout_mode = 2
theme_override_constants/separation = 20
alignment = 1

[node name="CancelButton" parent="HUD/Panel" instance=ExtResource("6_v0g5y")]
custom_minimum_size = Vector2(150, 50)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -165.0
offset_right = -15.0
offset_bottom = 50.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(75, 25)

[node name="TextureRect2" type="TextureRect" parent="HUD/Panel/CancelButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -12.5
offset_top = -13.0
offset_right = 12.5
offset_bottom = 13.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("7_b04qv")
expand_mode = 1
stretch_mode = 5

[node name="LoadingElement" parent="HUD/Panel" instance=ExtResource("8_f0qrt")]
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 33.0
offset_top = 2.0
offset_right = 73.0
offset_bottom = 42.0
grow_horizontal = 1
grow_vertical = 1

[node name="Details" type="Panel" parent="HUD"]
custom_minimum_size = Vector2(600, 600)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -300.0
offset_right = 300.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = ExtResource("3_6gxwk")

[node name="Title" type="Label" parent="HUD/Details"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -31.5
offset_right = 31.5
offset_bottom = 48.0
grow_horizontal = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_fo8s3")
theme_override_font_sizes/font_size = 30
text = "CALL_HISTORY"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Text" type="Label" parent="HUD/Details"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_top = 48.0
offset_right = -15.0
offset_bottom = -16.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_fo8s3")
theme_override_font_sizes/font_size = 30
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 1

[node name="DetailsCancelButton" parent="HUD/Details" instance=ExtResource("6_v0g5y")]
custom_minimum_size = Vector2(150, 50)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -165.0
offset_right = -15.0
offset_bottom = 50.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(75, 25)

[node name="TextureRect2" type="TextureRect" parent="HUD/Details/DetailsCancelButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -12.5
offset_top = -13.0
offset_right = 12.5
offset_bottom = 13.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("7_b04qv")
expand_mode = 1
stretch_mode = 5

[node name="HTTPRequest" type="HTTPRequest" parent="."]

[connection signal="pressed" from="HUD/Panel/CancelButton" to="." method="exit"]
[connection signal="pressed" from="HUD/Details/DetailsCancelButton" to="." method="_on_details_cancel_button_pressed"]
[connection signal="request_completed" from="HTTPRequest" to="." method="_on_http_request_request_completed"]
