# Email Removal Feature Test Script
# This script demonstrates the new email removal verification feature

extends Node

# This is a test script to show how the new email removal feature works
# The feature has been added to the name_popup.gd and name_popup.tscn

func test_email_removal_flow():
	print("=== Email Removal Feature Test ===")
	print("1. User clicks 'Remove Email' button")
	print("2. Step 1: Email confirmation screen appears")
	print("   - Shows current email (read-only)")
	print("   - User clicks 'Send Code' button")
	print("   - API call to /account/request_remove_code/")
	print("3. Step 2: Code verification screen appears")
	print("   - User enters 6-digit verification code")
	print("   - User clicks 'Remove Email' button")
	print("   - API call to /account/verify_remove_code/")
	print("4. If code is valid:")
	print("   - Final API call to /account/remove_email/")
	print("   - Email is removed from account")
	print("   - Success message shown")
	print("   - Returns to main popup")
	print("=== Feature Implementation Complete ===")

# API Endpoints that need to be implemented on backend:
# 1. POST /account/request_remove_code/
#    - Input: { "email": "<EMAIL>" }
#    - Action: Send verification code to email
#    - Response: 200 OK or error code
#
# 2. POST /account/verify_remove_code/
#    - Input: { "email": "<EMAIL>", "code": "123456" }
#    - Action: Verify the code is correct
#    - Response: 200 OK if valid, 400 if invalid
#
# 3. POST /account/remove_email/
#    - Input: { "email": "<EMAIL>", "code": "123456" }
#    - Action: Remove email from account after final verification
#    - Response: 200 OK if successful

# New UI Components Added:
# - RemoveEmailStep1: Email confirmation screen
# - RemoveEmailStep2: Code verification screen
# - Loading states and error handling for each step
# - Back buttons for navigation between steps

# New Translation Keys Added:
# - CONFIRM_EMAIL_REMOVAL
# - ENTER_REMOVE_CODE
# - SEND_CODE
# - ERROR_SENDING_CODE
# - INVALID_CODE
# - EMAIL_REMOVED_SUCCESS

func _ready():
	test_email_removal_flow()
