extends "res://Scenes/ui/base_popup.gd"

signal on_name_changed(name)
@onready var line_edit = $NinePatchRect/LineEdit
@onready var price_parent = $AcceptButton/Price
@onready var price_label = $AcceptButton/Price/Label
@onready var loading_element = $LoadingElement
@onready var accept_button = $AcceptButton
@onready var full_loading: FullScreenLoading = $Loading
@onready var remove_email_http: HTTPRequest = $LinkEmail/RemoveEmailHTTP

# Remove Email Verification Steps
@onready var remove_email_step1 = $RemoveEmailStep1
@onready var remove_email_step2 = $RemoveEmailStep2
@onready var remove_request_code_http = $RemoveRequestCodeHTTP
@onready var remove_verify_code_http = $RemoveVerifyCodeHTTP
@onready var remove_email_line_edit = $RemoveEmailStep1/EmailLineEdit
@onready var remove_code_line_edit = $RemoveEmailStep2/CodeLineEdit
@onready var remove_loading_step1 = $RemoveEmailStep1/LoadingElement
@onready var remove_loading_step2 = $RemoveEmailStep2/LoadingElement
@onready var remove_error_step1 = $RemoveEmailStep1/EmailLineEdit/ErrorLabel
@onready var remove_error_step2 = $RemoveEmailStep2/CodeLineEdit/ErrorLabel
@onready var remove_next_button_step1 = $RemoveEmailStep1/NextButton
@onready var remove_next_button_step2 = $RemoveEmailStep2/NextButton
@onready var remove_back_button_step1 = $RemoveEmailStep1/BackButton
@onready var remove_back_button_step2 = $RemoveEmailStep2/BackButton
@onready var remove_code_label = $RemoveEmailStep2/Label

#Step1
@onready var request_code_http_request = $RequestCodeHTTPRequest
@onready var link_step_1 = $LinkStep1
@onready var email_line_edit = $LinkStep1/EmailLineEdit
@onready var loading_element_step1 = $LinkStep1/LoadingElement
@onready var next_step_step_1_button = $LinkStep1/NextStepStep1Button
@onready var error_label_step_1 = $LinkStep1/EmailLineEdit/ErrorLabelStep1
#Step1

#Step2
@onready var verify_code_http_request = $VerifyCodeHTTPRequest
@onready var link_step_2 = $LinkStep2
@onready var back_button_step_2 = $LinkStep2/BackButtonStep2
@onready var code_line_edit = $LinkStep2/CodeLineEdit
@onready var next_step_step_2_button = $LinkStep2/NextStepStep2Button
@onready var loading_element_step2 = $LinkStep2/LoadingElement2
@onready var error_label_step_2 = $LinkStep2/CodeLineEdit/ErrorLabelStep2
@onready var code_label: Label = $LinkStep2/Label

#Step2
#Step3
@onready var link_step_3 = $LinkStep3
#Step3
@onready var link_email = $LinkEmail
@onready var link_button = $LinkButton
@onready var link_label: Label = $LinkButton/Label
@onready var email_label: Label = $LinkEmail/VBoxContainer/email


var last_email_used = ""
var remove_email_used = ""
var is_loading = false


func _ready():
	request_code_http_request.request_completed.connect(on_request_code_complete)
	verify_code_http_request.request_completed.connect(on_verify_code_complete)
	remove_request_code_http.request_completed.connect(on_remove_request_code_complete)
	remove_verify_code_http.request_completed.connect(on_remove_verify_code_complete)
	DataSaver.handle_changed.connect(on_handle_change_request)


func _process(_delta):
	if Selector.name_changed_count > 0:
		price_parent.visible = true
		price_label.text = str(Selector.change_name_tax)
	else:
		price_parent.visible = false
	
	if is_loading:
		accept_button.visible = false
		loading_element.visible = true
	else:
		accept_button.visible = true
		loading_element.visible = false


func _on_accept_pressed():
	if Selector.name_changed_count == 0:
		DataSaver.send_change_handle_request(line_edit.text)
		is_loading = true
		return
	
	if Selector.my_coin < Selector.change_name_tax:
		Selector.shop_mode = Selector.SHOP_ENUM.Coin
		get_tree().change_scene_to_file("res://Scenes/shop_scene.tscn")
		return

	Selector.my_coin -= Selector.change_name_tax
	DataSaver.send_change_handle_request(line_edit.text)
	is_loading = true


func on_handle_change_request():
	if Selector.my_coin < 0:
		Selector.my_coin = 0
	is_loading = false
	Selector.name_changed_count += 1
	emit_signal("on_name_changed", line_edit.text)
	visible = false



func _on_visibility_changed():
	if visible and line_edit:
		line_edit.text = DataSaver.get_item("handle", "")


func _on_exit_button_pressed():
	visible = false


func show_step1():
	link_step_1.visible = true
	next_step_step_1_button.visible = true
	loading_element_step1.visible = false
	error_label_step_1.visible = false
	email_line_edit.grab_focus()


func _on_link_button_pressed():
	show_step1()


func _on_back_button_step_1_pressed():
	link_step_1.visible = false


func _on_next_step_step_1_pressed():
	send_request_code_request()


func send_request_code_request():
	var url = Constants.BACKEND_URL + "/account/request_code/"
	var data = {
		"email": email_line_edit.text
	}
	last_email_used = email_line_edit.text
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	request_code_http_request.cancel_request()
	request_code_http_request.request(url, headers, HTTPClient.METHOD_POST, json)
	loading_element_step1.visible = true
	next_step_step_1_button.visible = false
	error_label_step_1.visible = false


func on_request_code_complete(_result, response_code, _headers, _body):
	loading_element_step1.visible = false
	next_step_step_1_button.visible = true
	if response_code == 200:
		link_step_2.visible = true
		error_label_step_2.visible = false
		next_step_step_2_button.visible = true
		loading_element_step2.visible = false
		code_line_edit.grab_focus()
		code_label.text = tr("ENTER_YOUR_CODE") % [email_line_edit.text]
		email_line_edit.text = ""
	else:
		error_label_step_1.visible = true


func _on_back_button_step_2_pressed():
	link_step_2.visible = false
	show_step1()


func send_verify_code_request():
	var url = Constants.BACKEND_URL + "/account/verify_code/"
	var data = {
		"email": last_email_used,
		"code": trim_code(code_line_edit.text)
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	verify_code_http_request.cancel_request()
	verify_code_http_request.request(url, headers, HTTPClient.METHOD_POST, json)
	loading_element_step2.visible = true
	next_step_step_2_button.visible = false
	error_label_step_2.visible = false


func trim_code(code):
	var ret = code
	ret = ret.replace(" ", "").replace(",", "").replace("\n", "")
	ret = ret.replace("۱", "1")
	ret = ret.replace("۲", "2")
	ret = ret.replace("۳", "3")
	ret = ret.replace("۴", "4")
	ret = ret.replace("۵", "5")
	ret = ret.replace("۶", "6")
	ret = ret.replace("۷", "7")
	ret = ret.replace("۸", "8")
	ret = ret.replace("۹", "9")
	ret = ret.replace("۰", "0")
	return ret


func on_verify_code_complete(_result, response_code, _headers, body):
	loading_element_step2.visible = false
	next_step_step_2_button.visible = true
	if response_code == 200:#Load old account
		print("load old data")
		var json = JSON.parse_string(body.get_string_from_utf8())
		DataSaver.set_item("JWT", json["token"], true)
		get_tree().change_scene_to_file("res://Scenes/decider_scene.tscn")
	if response_code == 201:#Link Success
		print("link success")
		DataSaver.set_item("email", last_email_used, true)
		link_step_3.visible = true
	if response_code == 400:
		error_label_step_2.visible = true


func _on_next_step_step_2_button_pressed():
	send_verify_code_request()


func _on_finish_button_pressed():
	link_step_1.visible = false
	link_step_2.visible = false
	link_step_3.visible = false
	visible = false


func show_popup():
	full_loading.visible = false
	link_step_1.visible = false
	link_step_2.visible = false
	link_step_3.visible = false
	remove_email_step1.visible = false
	remove_email_step2.visible = false
	visible = true
	if DataSaver.has_email():
		link_button.visible = false
		#link_label.text = tr("LINK_ANOTHER")
		link_email.visible = true
		email_label.text = DataSaver.get_item("email", "")
	else:
		link_button.visible = true
		link_email.visible = false
		link_label.text = tr("LINK")


func _on_hidden():
	DataSaver.send_save_request()


func _on_change_account_button_pressed() -> void:
	show_step1()


func _on_remove_email_pressed() -> void:
	show_remove_email_step1()


# Remove Email Step 1: Show email input and request verification code
func show_remove_email_step1():
	remove_email_step1.visible = true
	remove_next_button_step1.visible = true
	remove_loading_step1.visible = false
	remove_error_step1.visible = false
	remove_email_line_edit.text = DataSaver.get_item("email", "")
	remove_email_line_edit.grab_focus()


func _on_remove_back_button_step1_pressed():
	remove_email_step1.visible = false


func _on_remove_next_button_step1_pressed():
	send_remove_request_code()


func send_remove_request_code():
	var url = Constants.BACKEND_URL + "/account/request_remove_code/"
	var data = {
		"email": remove_email_line_edit.text
	}
	remove_email_used = remove_email_line_edit.text
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	remove_request_code_http.cancel_request()
	remove_request_code_http.request(url, headers, HTTPClient.METHOD_POST, json)
	remove_loading_step1.visible = true
	remove_next_button_step1.visible = false
	remove_error_step1.visible = false


func on_remove_request_code_complete(_result, response_code, _headers, _body):
	remove_loading_step1.visible = false
	remove_next_button_step1.visible = true
	if response_code == 200:
		show_remove_email_step2()
	else:
		remove_error_step1.visible = true


# Remove Email Step 2: Show code input and verify
func show_remove_email_step2():
	remove_email_step2.visible = true
	remove_next_button_step2.visible = true
	remove_loading_step2.visible = false
	remove_error_step2.visible = false
	remove_code_line_edit.text = ""
	remove_code_line_edit.grab_focus()
	remove_code_label.text = tr("ENTER_REMOVE_CODE") % [remove_email_used]


func _on_remove_back_button_step2_pressed():
	remove_email_step2.visible = false
	show_remove_email_step1()


func _on_remove_next_button_step2_pressed():
	send_remove_verify_code()


func send_remove_verify_code():
	var url = Constants.BACKEND_URL + "/account/verify_remove_code/"
	var data = {
		"email": remove_email_used,
		"code": trim_code(remove_code_line_edit.text)
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	remove_verify_code_http.cancel_request()
	remove_verify_code_http.request(url, headers, HTTPClient.METHOD_POST, json)
	remove_loading_step2.visible = true
	remove_next_button_step2.visible = false
	remove_error_step2.visible = false


func on_remove_verify_code_complete(_result, response_code, _headers, _body):
	remove_loading_step2.visible = false
	remove_next_button_step2.visible = true
	if response_code == 200:
		# Code verified, now actually remove the email
		send_remove_email_request()
	else:
		remove_error_step2.visible = true


func send_remove_email_request():
	full_loading.visible = true
	var url = Constants.BACKEND_URL + "/account/remove_email/"
	var data = {
		"email": remove_email_used,
		"code": trim_code(remove_code_line_edit.text)
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	remove_email_http.cancel_request()
	remove_email_http.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_remove_email_http_request_completed(_result: int, response_code: int, _headers: PackedStringArray, _body: PackedByteArray) -> void:
	full_loading.visible = false
	if response_code == 200:
		DataSaver.set_item("email", null, true)
		remove_email_step1.visible = false
		remove_email_step2.visible = false
		show_popup()
		Constants.show_toast(tr("EMAIL_REMOVED_SUCCESS"))
		return

	Constants.show_toast(str(response_code))
