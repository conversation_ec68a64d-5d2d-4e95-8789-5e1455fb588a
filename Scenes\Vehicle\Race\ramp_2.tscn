[gd_scene load_steps=6 format=4 uid="uid://bkumfkiv7ck5p"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_o5jhq"]
resource_name = "lambert29"
cull_mode = 2
albedo_color = Color(0.241658, 0.241658, 0.241658, 1)
roughness = 0.635355

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_s3ab6"]
resource_name = "lambert30"
cull_mode = 2
albedo_color = Color(0.492429, 0.492429, 0.492429, 1)
roughness = 0.662878

[sub_resource type="ArrayMesh" id="ArrayMesh_qyycx"]
_surfaces = [{
"aabb": AABB(-670.006, -615.055, -405.171, 1340.01, 1230.11, 542.446),
"format": 34896613377,
"index_count": 48,
"index_data": PackedByteArray("AQAMAA8AAQADAAwADgACAAAADgANAAIAAQAOAAAAAQAPAA4AAgAMAAMAAgANAAwABQAJAAQABQAIAAkABgAJAAoABgAEAAkABgALAAcABgAKAAsACAAHAAsACAAFAAcA"),
"name": "lambert29",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("AAD/////AAD///////8AAAAA///a/AAA/////wAAAAAAAAAA2vwAAP//AAAAAAAAAAAAAP//AAD//wAA//8AAP//UCQAAAAAAABQJNr8AAAAAFAk//8AAP//UCT//wAA//+u2wAAAAAAAK7b2vwAAAAArtv//wAA//+u2///AAA=")
}, {
"aabb": AABB(-670.006, -615.055, -405.171, 1340.01, 1230.11, 542.446),
"format": 34896613377,
"index_count": 36,
"index_data": PackedByteArray("BgAFAAQABgAHAAUAAgABAAAAAgADAAEACAANAAkACAAMAA0ACgANAA4ACgAJAA0ACgAPAAsACgAOAA8ADAALAA8ADAAIAAsA"),
"name": "lambert30",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("AAD/////AAD///////8AAAAA///a/AAA/////wAAAAAAAAAA2vwAAP//AAAAAAAAAAAAAP//AAD//wAA//8AAP//UCQAAAAAAABQJNr8AAAAAFAk//8AAP//UCT//wAA//+u2wAAAAAAAK7b2vwAAAAArtv//wAA//+u2///AAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_w6g02"]
resource_name = "Ramp_checkpointglb_Mesh_004"
_surfaces = [{
"aabb": AABB(-670.006, -615.055, -405.171, 1340.01, 1230.11, 542.446),
"attribute_data": PackedByteArray("/18AAP9f////nwAA/5////9f/7//X/+//5//v/+f/7//X/9//x//v//f/7//n/9//1//P/8f////n/8//9///6nK/7//n1SV/19UlVU1/7//X6oqVTX///+fqiqpyv//VLX/v/+fqqr/X6mqqkr/v/9fVRWqSv///59VFVS1//8="),
"format": 34896613399,
"index_count": 48,
"index_data": PackedByteArray("AwAYAB8AAwAGABgAHQAFAAEAHQAbAAUAAgAcAAAAAgAeABwABAAZAAcABAAaABkACwASAAgACwARABIADQATABUADQAJABMADAAWAA4ADAAUABYAEAAPABcAEAAKAA8A"),
"material": SubResource("StandardMaterial3D_o5jhq"),
"name": "lambert29",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("AAD/////AIAAAP////9U1f///////wCA////////VNUAAP//2vz//wAA///a/FTV/////wAAVNX/////AAD//wAAAADa/P//AAAAANr8VNX//wAAAABU1f//AAAAAP//AAAAAP//AIAAAAAA//9U1f//AAD//wCA//8AAP//VNX//1AkAABU1f//UCQAAP//AABQJNr8//8AAFAk2vxU1QAAUCT//wCAAABQJP//VNX//1Ak//8AgP//UCT//1TV//+u2wAAVNX//67bAAD//wAArtva/P//AACu29r8VNUAAK7b//8AgAAArtv//1TV//+u2///AID//67b//9U1f9///9U1VTV/3///6qqVFX//1VrVNVU1aqqVFX+/1Vr//+plFTVVNWqqlRV//9Va/9///9U1VTV/3///6qqVFWqqlRV//9Va///VWtU1VTV/3///1TVVNX/f///qqpUVaqqVFX+/1Vr//9Va1TVVNX/f///VNVU1f9///+qqlRV")
}, {
"aabb": AABB(-670.006, -615.055, -405.171, 1340.01, 1230.11, 542.446),
"attribute_data": PackedByteArray("/1////+f////X/+//5//v/9f/3//n/9//1//P/+f/z+pyv+//59Ulf9fVJVVNf+//1+qKlU1////n6oqqcr//1S1/7//n6qq/1+pqqpK/7//X1UVqkr///+fVRVUtf//"),
"format": 34896613399,
"index_count": 36,
"index_data": PackedByteArray("BgAFAAQABgAHAAUAAgABAAAAAgADAAEACQASAAoACQARABIADQATABUADQALABMADAAWAA4ADAAUABYAEAAPABcAEAAIAA8A"),
"material": SubResource("StandardMaterial3D_s3ab6"),
"name": "lambert30",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("AAD//////7////////9xwQAA///a/A3A/////wAAdMEAAAAA2vx0wf//AAAAAP+/AAAAAP//1sD//wAA////v///UCQAAFTV//9QJAAA//8AAFAk2vz//wAAUCTa/FTVAABQJP//AIAAAFAk//9U1f//UCT//wCA//9QJP//VNX//67bAABU1f//rtsAAP//AACu29r8//8AAK7b2vxU1QAArtv//wCAAACu2///VNX//67b//8AgP//rtv//1TV////fz7uf1yS+yZ3NO5qXJUjNG4rAOl/yBybcQAA/3+qqlRV/v9Va/7/VWtU1VTV/3///1TVVNX/f///qqpUVaqqVFX+/1Vr/v9Va1TVVNX/f///VNVU1f9///+qqlRV")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_qyycx")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_lwd1h"]
data = PackedVector3Array(670.006, 615.055, 137.276, 670.006, 440.548, -405.171, 670.006, 440.548, 137.276, 670.006, 615.055, 137.276, 670.006, 615.055, -405.171, 670.006, 440.548, -405.171, -670.006, 440.548, 137.276, -670.006, 615.055, 130.613, -670.006, 615.055, 137.276, -670.006, 440.548, 137.276, -670.006, 440.548, 130.613, -670.006, 615.055, 130.613, 670.006, 615.055, 137.276, -670.006, 440.548, 137.276, -670.006, 615.055, 137.276, 670.006, 615.055, 137.276, 670.006, 440.548, 137.276, -670.006, 440.548, 137.276, -670.006, 615.055, 130.613, 670.006, 440.548, -405.171, 670.006, 615.055, -405.171, -670.006, 615.055, 130.613, -670.006, 440.548, 130.613, 670.006, 440.548, -405.171, 670.006, -615.055, -405.171, -670.006, -440.566, 130.613, -670.006, -615.055, 130.613, 670.006, -615.055, -405.171, 670.006, -440.566, -405.171, -670.006, -440.566, 130.613, -670.006, -615.055, 137.276, -670.006, -440.566, 130.613, -670.006, -440.566, 137.276, -670.006, -615.055, 137.276, -670.006, -615.055, 130.613, -670.006, -440.566, 130.613, -670.006, -615.055, 137.276, 670.006, -440.566, 137.276, 670.006, -615.055, 137.276, -670.006, -615.055, 137.276, -670.006, -440.566, 137.276, 670.006, -440.566, 137.276, 670.006, -440.566, -405.171, 670.006, -615.055, 137.276, 670.006, -440.566, 137.276, 670.006, -440.566, -405.171, 670.006, -615.055, -405.171, 670.006, -615.055, 137.276, -670.006, -615.055, 137.276, 670.006, -615.055, -405.171, -670.006, -615.055, 130.613, -670.006, -615.055, 137.276, 670.006, -615.055, 137.276, 670.006, -615.055, -405.171, -670.006, 615.055, 130.613, 670.006, 615.055, 137.276, -670.006, 615.055, 137.276, -670.006, 615.055, 130.613, 670.006, 615.055, -405.171, 670.006, 615.055, 137.276, 670.006, -440.566, -405.171, -670.006, 440.548, 130.613, -670.006, -440.566, 130.613, 670.006, -440.566, -405.171, 670.006, 440.548, -405.171, -670.006, 440.548, 130.613, -670.006, -440.566, 137.276, -670.006, 440.548, 130.613, -670.006, 440.548, 137.276, -670.006, -440.566, 137.276, -670.006, -440.566, 130.613, -670.006, 440.548, 130.613, -670.006, -440.566, 137.276, 670.006, 440.548, 137.276, 670.006, -440.566, 137.276, -670.006, -440.566, 137.276, -670.006, 440.548, 137.276, 670.006, 440.548, 137.276, 670.006, 440.548, -405.171, 670.006, -440.566, 137.276, 670.006, 440.548, 137.276, 670.006, 440.548, -405.171, 670.006, -440.566, -405.171, 670.006, -440.566, 137.276)

[node name="Ramp2" type="MeshInstance3D" groups=["VisibleGroup1"]]
transform = Transform3D(0.01, 0, 0, 0, 0, -0.01, 0, 0.01, 0, 0, 1.306, 0)
mesh = SubResource("ArrayMesh_w6g02")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_lwd1h")
