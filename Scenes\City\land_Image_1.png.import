[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://bo5i8pcl7eddf"
path.s3tc="res://.godot/imported/land_Image_1.png-8b9521b838604d655e3a03dcac239ce5.s3tc.ctex"
path.etc2="res://.godot/imported/land_Image_1.png-8b9521b838604d655e3a03dcac239ce5.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "39aa318c14bf9bce67bb4d54fcb051cd"
}

[deps]

source_file="res://Scenes/City/land_Image_1.png"
dest_files=["res://.godot/imported/land_Image_1.png-8b9521b838604d655e3a03dcac239ce5.s3tc.ctex", "res://.godot/imported/land_Image_1.png-8b9521b838604d655e3a03dcac239ce5.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
