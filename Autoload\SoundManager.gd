extends Node

var click_sound = preload("res://Scenes/ui/assets/Bonus/click1.ogg")
var jump_sound = preload("res://assets/sfx/jump.mp3")
var crash_sound = preload("res://assets/sfx/crash.mp3")
var boing_sound = preload("res://assets/sfx/boing.mp3")
var cash_sound = preload("res://assets/sfx/freeride/cash.mp3")
var woosh_sound = preload("res://assets/sfx/woosh.mp3")
var attack_sound = preload("res://assets/sfx/attack.mp3")
var cook_sound = preload("res://assets/sfx/ding.mp3")
var order_sound = preload("res://assets/sfx/ResturantBell.mp3")
var hunger_sound = preload("res://assets/sfx/stomach.mp3")
var bg_music = preload("res://assets/sfx/menu.ogg")
var freeride_general_music = preload("res://assets/sfx/freeride/general.mp3")
var freeride_bastan_music = preload("res://assets/sfx/freeride/bastan.mp3")
var freeride_chaikhane_music = preload("res://assets/sfx/freeride/chaikhune.mp3")
var freeride_playground_music = preload("res://assets/sfx/freeride/playground.mp3")
var freeride_islamic_music = preload("res://assets/sfx/freeride/islam.mp3")
var freeride_persian_music = preload("res://assets/sfx/freeride/persian.mp3")
var freeride_zoorkhoone_music = preload("res://assets/sfx/freeride/zoorkhoone.mp3")
var audio_stream: AudioStreamPlayer
var bg_player
var freeride_player: AudioStreamPlayer

enum SOUND_TYPE {COOK, ORDER, HUNGER, COIN, DIAL, PoliceTel,
	Pistol, Arm, Melee, Camera, Busy, Ringtone, SnowballThrow, SnowballHit,
	ExplosionMedium, ExplosionBig, MissleWhistle, WhistleExplode, FireworkCandle,
	 FireworkExplosion, DiceRoll, MenchMove, MenchKick, Horn1, Horn2, Horn3, Horn4,
	Checkpoint}

func _ready():
	audio_stream = AudioStreamPlayer.new()
	add_child(audio_stream)
	audio_stream.stream = AudioStreamPolyphonic.new()
	audio_stream.stream.polyphony = 10
	audio_stream.play()

	bg_player = AudioStreamPlayer.new()
	add_child(bg_player)
	bg_player.stream = bg_music
	freeride_player = AudioStreamPlayer.new()
	add_child(freeride_player)


func play_attack_sound():
	audio_stream.get_stream_playback().play_stream(attack_sound)


func play_click_sound():
	audio_stream.get_stream_playback().play_stream(click_sound)


func play_jump_sound():
	audio_stream.get_stream_playback().play_stream(jump_sound)


func play_mobile_beep_sound():
	audio_stream.get_stream_playback().play_stream(preload("res://assets/sfx/drop.ogg"))


func play_crash_sound():
	audio_stream.get_stream_playback().play_stream(crash_sound)


func play_boing_sound():
	audio_stream.get_stream_playback().play_stream(boing_sound)


func play_woosh_sound():
	audio_stream.get_stream_playback().play_stream(woosh_sound)


func play_bg_music():
	if bg_player.playing:
		return
	bg_player.play()


func stop_bg_music():
	bg_player.stop()


var weddingMusicSelector: WeddingMusicSelector
func play_freeride_music(type: MusicStand.MusicType):

	var stream = freeride_general_music
	match type:
		MusicStand.MusicType.General:
			stream = freeride_general_music
		MusicStand.MusicType.Bastan:
			stream = freeride_bastan_music
		MusicStand.MusicType.Chaikhane:
			stream = freeride_chaikhane_music
		MusicStand.MusicType.Playground:
			stream = freeride_playground_music
		MusicStand.MusicType.PersianGarden:
			stream = freeride_persian_music
		MusicStand.MusicType.Islamic:
			stream = freeride_islamic_music
		MusicStand.MusicType.Zoorkhoone:
			stream = freeride_zoorkhoone_music
		MusicStand.MusicType.Talar:
			if weddingMusicSelector:
				stream = weddingMusicSelector.get_current_music()
		MusicStand.MusicType.Snowfield:
			stream = preload("res://assets/sfx/freeride/snowfield.mp3")

	if stream == null:
		return
	if stream != freeride_player.stream:
		freeride_player.stream = stream
		freeride_player.play()
	
	#client
	if type == MusicStand.MusicType.Talar:
		if abs(freeride_player.get_playback_position() - weddingMusicSelector.current_seek) >= weddingMusicSelector.client_seek_diff_thr:
			freeride_player.seek(weddingMusicSelector.current_seek)


func stop_freeride_music():
	freeride_player.stream = null
	freeride_player.stop()


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_PRIVATE_CHAT)
func play_sound(sound):
	var stream = null
	match sound:
		SOUND_TYPE.ORDER:
			stream = order_sound
		SOUND_TYPE.COOK:
			stream = cook_sound
		SOUND_TYPE.HUNGER:
			stream = hunger_sound
		SOUND_TYPE.COIN:
			stream = cash_sound
		SOUND_TYPE.Melee:
			stream = attack_sound
		SOUND_TYPE.DIAL:
			stream = preload("res://assets/sfx/freeride/dial.mp3")
		SOUND_TYPE.PoliceTel:
			stream = preload("res://assets/sfx/freeride/policetel.mp3")
		SOUND_TYPE.Pistol:
			stream = preload("res://assets/sfx/freeride/pistol.mp3")
		SOUND_TYPE.Arm:
			stream = preload("res://assets/sfx/freeride/arm.mp3")
		SOUND_TYPE.Camera:
			stream = preload("res://assets/sfx/freeride/capture.mp3")
		SOUND_TYPE.Ringtone:
			stream = preload("res://assets/sfx/freeride/mobile_ring.mp3")
		SOUND_TYPE.Busy:
			stream = preload("res://assets/sfx/freeride/busy.mp3")
		SOUND_TYPE.SnowballHit:
			stream = preload("res://assets/sfx/freeride/snowball_hit.mp3")
		SOUND_TYPE.SnowballThrow:
			stream = preload("res://assets/sfx/freeride/snowball_throw.mp3")
		SOUND_TYPE.ExplosionMedium:
			stream = preload("res://assets/sfx/freeride/fireworks/explosion_medium.mp3")
		SOUND_TYPE.ExplosionBig:
			stream = preload("res://assets/sfx/freeride/fireworks/explosion_big.mp3")
		SOUND_TYPE.MissleWhistle:
			stream = preload("res://assets/sfx/freeride/fireworks/missle_whistle_start.mp3")
		SOUND_TYPE.WhistleExplode:
			stream = preload("res://assets/sfx/freeride/fireworks/whistle_explode.mp3")
		SOUND_TYPE.FireworkCandle:
			stream = preload("res://assets/sfx/freeride/fireworks/firework_candle.mp3")
		SOUND_TYPE.FireworkExplosion:
			stream = preload("res://assets/sfx/freeride/fireworks/firework_shower.mp3")
		SOUND_TYPE.DiceRoll:
			stream = preload("res://Scenes/ui/assets/mench/dice.mp3")
		SOUND_TYPE.MenchMove:
			stream = preload("res://Scenes/ui/assets/mench/menchmove.mp3")
		SOUND_TYPE.MenchKick:
			stream = preload("res://Scenes/ui/assets/mench/menchkick.mp3")
		SOUND_TYPE.Horn1:
			stream = preload("res://assets/sfx/horn1.mp3")
		SOUND_TYPE.Horn2:
			stream = preload("res://assets/sfx/horn2.mp3")
		SOUND_TYPE.Horn3:
			stream = preload("res://assets/sfx/horn3.mp3")
		SOUND_TYPE.Horn4:
			stream = preload("res://assets/sfx/horn4.mp3")
		SOUND_TYPE.Checkpoint:
			stream = preload("res://assets/sfx/checkpoint.mp3")

	if stream == null:
		return
	audio_stream.get_stream_playback().play_stream(stream)


#Server
@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_SOUND)
func play_3d_sound(position, distance, soundType):
	var server = Constants.server
	for key in server.players_data.keys():
		if not server.is_bot(key):
			var pos = server.players[key].global_position as Vector3
			if (pos - position).length_squared() <= distance * distance:
				play_sound.rpc_id(key, soundType)


func update_sound_volume():
	audio_stream.volume_db = linear_to_db(DataSaver.get_item("sound_volume"))


func update_music_volume():
	bg_player.volume_db = linear_to_db(DataSaver.get_item("music_volume"))
	freeride_player.volume_db = linear_to_db(DataSaver.get_item("music_volume"))
