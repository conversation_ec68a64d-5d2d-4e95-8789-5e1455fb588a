extends Control
class_name VehicleTouch

signal unmount
signal action1
signal action2
signal action3

@onready var pedal_container: HBoxContainer = $PedalContainer
@onready var steer_container: HBoxContainer = $SteerContainer
@onready var action1_parent: TextureRect = $ActionContainer/Action1
@onready var action2_parent: TextureRect = $ActionContainer/Action2
@onready var action3_parent: TextureRect = $ActionContainer/Action3
@onready var exit_button: TextureRect = $FirstRowContainer/Exit


var is_gas = false
var is_brake = false
var is_action1 = false
var is_action2 = false
var is_action3 = false
var is_unmount = false
var is_right = false
var is_left = false


func _ready() -> void:
	pass # Replace with function body.


func set_driver_mode(is_driver:bool):
	pedal_container.visible = is_driver
	steer_container.visible = is_driver
	action1_parent.visible = is_driver
	action2_parent.visible = is_driver
	if is_driver:
		var vehicle = VehicleManager.my_vehicle() as MyVehicle
		action2_parent.visible = vehicle.has_horn
		action3_parent.visible = vehicle.has_stunt
	else:
		action2_parent.visible = false
		action3_parent.visible = false


func _on_gas_pedal_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			is_gas = true
		else:
			is_gas = false


func _on_brake_pedal_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			is_brake = true
		else:
			is_brake = false


#Light
var last_action1_pressed = 0
func _on_action_1_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			is_action1 = true
			if Time.get_ticks_msec() - last_action1_pressed > 100:
				last_action1_pressed = Time.get_ticks_msec()
				action1.emit()
		else:
			is_action1 = false


#Horn
var last_action2_pressed = 0
func _on_action_2_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			is_action2 = true
			if Time.get_ticks_msec() - last_action2_pressed > 100:
				last_action2_pressed = Time.get_ticks_msec()
				action2.emit()
		else:
			is_action2 = false


#Stunt
var last_action3_pressed = 0
func _on_action_3_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			is_action3 = true
			if Time.get_ticks_msec() - last_action3_pressed > 100:
				last_action3_pressed = Time.get_ticks_msec()
				action3.emit()
		else:
			is_action3 = false


var last_exit_pressed = 0
func _on_exit_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			is_unmount = true
			if Time.get_ticks_msec() - last_exit_pressed > 500:
				last_exit_pressed = Time.get_ticks_msec()
				unmount.emit()
		else:
			is_unmount = false


func _on_right_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			is_right = true
		else:
			is_right = false


func _on_left_gui_input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		if event.pressed:
			is_left = true
		else:
			is_left = false
