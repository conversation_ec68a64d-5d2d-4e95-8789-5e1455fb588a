extends MeshInstance3D
class_name RaceCheckpoint

@export var vehicleRace:VehicleRace
@export var id: int
@export var y_adder = 90

func _ready() -> void:
	pass # Replace with function body.


func _on_area_3d_body_entered(p: Node3D) -> void:
	if Constants.is_server or (p is Character == false):
		return
	
	var player: Character = p
	
	if player.is_me():
		if VehicleManager.ami_in_vehicle() == false:
			Constants.show_toast(tr("VEHICLE_RACE_ERROR"))
			return
		
		vehicleRace.state_machine.checkpoint.rpc_id(1, id)
