extends Control

signal ban_request_completed

@onready var container = $AdminPanel/ScrollContainer/VBoxContainer
@onready var ban_http = $BanHTTPRequest
@onready var unban_http = $UNBanHTTPRequest
@onready var selected_name_label = $AdminPanel/Selected/SelectedName
@onready var selected_id_label = $AdminPanel/Selected/SelectedID
@onready var selected_character = $AdminPanel/Selected/Character
@onready var tick = $AdminPanel/Selected/Tick
@onready var title_http_request = $TitleHTTPRequest
@onready var undercover_mode: CheckBox = $AdminPanel/Selected/UndercoverMode
@onready var elite_panel: Panel = $AdminPanel/ElitePanel
@onready var elite_button: Button = $AdminPanel/EliteButton


var selected_data = null
var selected_job = null
func _ready():
	ban_http.request_completed.connect(on_ban_complete)
	unban_http.request_completed.connect(on_unban_complete)
	
	elite_panel.visible = false


func _process(_delta):
	pass


func show_popup():
	process_mode = PROCESS_MODE_INHERIT
	visible = true
	on_member_button_pressed(null)
	tick.visible = false
	for ch in container.get_children():
		ch.queue_free()
	
	undercover_mode.visible = Selector.stealth
	undercover_mode.button_pressed = Selector.undercover
	elite_button.visible = Selector.elite_bazras
	_on_undercover_mode_toggled(Selector.undercover)
	
	for key in Constants.client.remote_players_data.keys():
		if not Constants.client.remote_players_data[key].has("selection"):
			continue
		var b = load("res://Scenes/FreeRide/Utils/Minimap/AdminUserButton.tscn").instantiate()
		container.add_child(b)
		var data = Constants.client.remote_players_data[key]
		b.data = {
			"data": data,
			"key": key,
		}
		var player = Constants.client.remote_players_scenes[key]
		if not is_instance_valid(player):
			continue
		if player.cosmetic_data == null:
			print_debug("cosmetic null")
			continue

		b.pressed_data.connect(on_member_button_pressed)
		b.set_handle(data["selection"]["name"])
		if player.cosmetic_data.has("title"):
			if player.cosmetic_data["title"]:
				if player.cosmetic_data["title"]["title"] != "":
					var title = player.cosmetic_data["title"]["title"]
					b.set_job(title)
		b.set_id(str(data["backend_id"]))


func on_member_button_pressed(data):
	if data == null:
		selected_data = null
		selected_name_label.text = ""
		selected_id_label.text = ""
		selected_character.text = ""
		selected_job = null
		return
	for_me = false
	tick.visible = false
	selected_data = data
	selected_name_label.text = data["data"]["selection"]["name"]
	selected_id_label.text = str(data["data"]["backend_id"])
	selected_character.text = find_character_name(data["data"]["selection"]["character"])


func find_character_name(path):
	for ch in Selector.Characters:
		if ch["path"] == path:
			return ch["name"]
	return "Not Found"


func send_ban_user_request(backend_id, time, username=null):
	tick.visible = false
	var url = Constants.BACKEND_URL + "/game/free_admin_ban/"
	var data = {
		"time": time #Hours
	}
	
	if username == null:
		data["backend_id"] = backend_id
	else:
		data["username"] = username
		
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	ban_http.cancel_request()
	ban_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_ban_complete(_result, response_code, _headers, _body):
	if response_code == 200:
		Constants.show_toast("مسدود شد")
		tick.visible = true
		if selected_data:
			check_for_ban.rpc_id(selected_data["key"])
	ban_request_completed.emit()


func send_unban_user_request(backend_id, username=null):
	tick.visible = false
	var url = Constants.BACKEND_URL + "/game/free_admin_unban/"
	var data = {}
	if username == null:
		data["backend_id"] = backend_id
	else:
		data["username"] = username
	
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	ban_http.cancel_request()
	ban_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_unban_complete(_result, response_code, _headers, _body):
	if response_code == 200:
		Constants.show_toast("موفق")
		tick.visible = true
		if selected_data:
			check_for_ban.rpc_id(selected_data["key"])
	ban_request_completed.emit()


func _on_ban_button_pressed():
	if selected_data == null:
		return
	send_ban_user_request(selected_data["data"]["backend_id"], 4)


func _on_un_ban_button_pressed():
	if selected_data == null:
		return
	send_unban_user_request(selected_data["data"]["backend_id"])


func _on_kick_button_pressed():
	if selected_data == null:
		return
	dc.rpc_id(selected_data["key"])


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_PRIVATE_CHAT)
func check_for_ban():
	ClientBackendManager.send_amiban_request()


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_PRIVATE_CHAT)
func dc():
	var freeride = Constants.client.game_scene
	freeride.on_quit_pressed()


func _on_add_title_pressed():
	if selected_job == null:
		return
	if selected_data == null:
		return
	var title = selected_job["title"]
	var color = selected_job["color"]
	send_title_request(selected_data["data"]["backend_id"], title, color)


func _on_clear_title_button_pressed():
	if selected_data == null:
		return
	selected_job = null
	send_title_request(selected_data["data"]["backend_id"], "", "FFFFFF")


func send_title_request(backend_id, title, title_color="FFFFFF", username=null):
	tick.visible = false
	var url = Constants.BACKEND_URL + "/game/free_admin_set_title/"
	var data = {
		"title": title,
		"title_color": title_color,
	}
	if username == null:
		data["backend_id"] = backend_id
	else:
		data["username"] = username
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	title_http_request.cancel_request()
	title_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_title_http_request_request_completed(_result, response_code, _headers, _body):
	if response_code == 200:
		Constants.show_toast("موفق")
		tick.visible = true
		if for_me:
			ClientBackendManager.send_amiban_request()
		else:
			if selected_data:
				check_for_ban.rpc_id(selected_data["key"])
	ban_request_completed.emit()


func _on_job_index_pressed(index: int) -> void:
	selected_job = JobManager.jobs_data[index]


var for_me = false
func _on_shahrdar_pressed() -> void:
	for_me = true
	send_title_request(DataSaver.get_item("id", 0), "رییس جمهور", "FFFF00")


func _on_ban_5m_button_pressed() -> void:
	if selected_data == null:
		return
	send_ban_user_request(selected_data["data"]["backend_id"], 1.0/12)


func _on_ban_30m_button_pressed() -> void:
	if selected_data == null:
		return
	send_ban_user_request(selected_data["data"]["backend_id"], 1.0/2)


func _on_ban_1h_button_pressed() -> void:
	if selected_data == null:
		return
	send_ban_user_request(selected_data["data"]["backend_id"], 1)


func _on_ban_4h_button_pressed() -> void:
	if selected_data == null:
		return
	send_ban_user_request(selected_data["data"]["backend_id"], 4)


func _on_ban_1d_button_pressed() -> void:
	if selected_data == null:
		return
	send_ban_user_request(selected_data["data"]["backend_id"], 24)


func _on_undercover_mode_toggled(toggled_on: bool) -> void:
	Selector.undercover = toggled_on


func _on_elite_button_pressed() -> void:
	elite_panel.visible = true
