[gd_scene load_steps=38 format=3 uid="uid://bkhf6i207l7qo"]

[ext_resource type="Script" path="res://Scenes/Vehicle/Manager/VehicleManager.gd" id="1_v6q8o"]
[ext_resource type="Script" path="res://Inventory/Items/Vehicle/VehicleItem.gd" id="2_6c7y3"]
[ext_resource type="Resource" uid="uid://bl5243jhnxfme" path="res://Inventory/Items/Vehicle/CG125.tres" id="3_xoj10"]
[ext_resource type="Resource" uid="uid://de5f3cwlwla56" path="res://Inventory/Items/Vehicle/Vespa.tres" id="4_arond"]
[ext_resource type="Resource" uid="uid://wk8kdbatrm2l" path="res://Inventory/Items/Vehicle/Trail.tres" id="5_xcc8q"]
[ext_resource type="Resource" path="res://Inventory/Items/Vehicle/Click.tres" id="6_v5jli"]
[ext_resource type="Resource" path="res://Inventory/Items/Vehicle/Atv.tres" id="7_g8rxc"]
[ext_resource type="Resource" path="res://Inventory/Items/Vehicle/Ninja.tres" id="8_bxwls"]
[ext_resource type="Resource" path="res://Inventory/Items/Vehicle/Pishva.tres" id="9_lj8mm"]
[ext_resource type="Resource" uid="uid://b83tr6m2r8h7q" path="res://Inventory/Items/Vehicle/Apache.tres" id="10_0m2lb"]
[ext_resource type="Resource" uid="uid://dfr6afxecdlgq" path="res://Inventory/Items/Vehicle/PoliceMotor.tres" id="11_yunsi"]
[ext_resource type="Resource" uid="uid://bd2s723axsg86" path="res://Inventory/Items/Vehicle/Skateboard.tres" id="12_3uqkv"]
[ext_resource type="Resource" uid="uid://bxs3pbpg7g6ls" path="res://Inventory/Items/Vehicle/Scooter.tres" id="13_uljah"]
[ext_resource type="PackedScene" uid="uid://bkop6titbax25" path="res://Scenes/Vehicle/Scenes/ElectScooter/ClientElectScooter.tscn" id="14_lcots"]
[ext_resource type="PackedScene" uid="uid://bn7krkknfaslg" path="res://Scenes/Vehicle/Scenes/ElectScooter/ElectScooter.tscn" id="15_88plq"]
[ext_resource type="Texture2D" uid="uid://dd6qygc17pct8" path="res://Scenes/Vehicle/Scenes/ElectScooter/Electscooterrender.png" id="16_6k5fs"]
[ext_resource type="PackedScene" uid="uid://sy3wibg6syx5" path="res://Scenes/Vehicle/Scenes/BatmanMotor/ClientBatmanMotor.tscn" id="17_mk67c"]
[ext_resource type="PackedScene" uid="uid://6ox4o8vweji7" path="res://Scenes/Vehicle/Scenes/BatmanMotor/BatmanMotor.tscn" id="18_thnhl"]
[ext_resource type="Texture2D" uid="uid://dotdamyfwulxn" path="res://Scenes/Vehicle/Scenes/BatmanMotor/BatmanMTrender.png" id="19_qmq4g"]
[ext_resource type="PackedScene" uid="uid://cmfs7dhk5vjc4" path="res://Scenes/Vehicle/Scenes/CB1300/ClientCB1300.tscn" id="20_yvkm1"]
[ext_resource type="PackedScene" uid="uid://e48ybvhfqd67" path="res://Scenes/Vehicle/Scenes/CB1300/CB1300.tscn" id="21_5e8ek"]
[ext_resource type="Texture2D" uid="uid://66bciecj0ms8" path="res://Scenes/Vehicle/Scenes/CB1300/Cb1300render.png" id="22_a28cf"]
[ext_resource type="PackedScene" uid="uid://ckpne5gga06y8" path="res://Scenes/Vehicle/Scenes/Harley/ClientHarley.tscn" id="23_f3nyg"]
[ext_resource type="PackedScene" uid="uid://j2u2hdlvg5db" path="res://Scenes/Vehicle/Scenes/Harley/Harley.tscn" id="24_r7fdb"]
[ext_resource type="Texture2D" uid="uid://c26ce4t3trmwf" path="res://Scenes/Vehicle/Scenes/Harley/harleyrender.png" id="25_rtk03"]
[ext_resource type="PackedScene" uid="uid://c444pj4bp3p6n" path="res://Scenes/Vehicle/Scenes/3Weel/Client3Weel.tscn" id="26_82ig3"]
[ext_resource type="PackedScene" uid="uid://btb1hhmbyyj5p" path="res://Scenes/Vehicle/Scenes/3Weel/3Weel.tscn" id="27_x8s1l"]
[ext_resource type="Texture2D" uid="uid://ho0wys135wma" path="res://Scenes/Vehicle/Scenes/3Weel/3wheelrender.png" id="28_w1qdh"]
[ext_resource type="PackedScene" uid="uid://bqj1sb032wssi" path="res://Scenes/Vehicle/Scenes/Forghoon/ClientForghoon.tscn" id="29_b6f2d"]
[ext_resource type="PackedScene" uid="uid://c48f2iqq12d4h" path="res://Scenes/Vehicle/Scenes/Forghoon/Forghoon.tscn" id="30_misr5"]
[ext_resource type="Texture2D" uid="uid://b5vap4a8up0cq" path="res://Scenes/Vehicle/Scenes/Forghoon/forghoonrender.png" id="31_qn85b"]

[sub_resource type="Resource" id="Resource_js4fd"]
script = ExtResource("2_6c7y3")
VehicleScene = ExtResource("15_88plq")
ClientSyncScene = ExtResource("14_lcots")
image = ExtResource("16_6k5fs")
id = 12
name = "ElectScooter"
price = 0
type = "Food"
in_hand = true
usable = false
remove_on_ban = false

[sub_resource type="Resource" id="Resource_6qgqn"]
script = ExtResource("2_6c7y3")
VehicleScene = ExtResource("18_thnhl")
ClientSyncScene = ExtResource("17_mk67c")
image = ExtResource("19_qmq4g")
id = 13
name = "BatmanMotor"
price = 0
type = "Food"
in_hand = true
usable = false
remove_on_ban = false

[sub_resource type="Resource" id="Resource_vdw4y"]
script = ExtResource("2_6c7y3")
VehicleScene = ExtResource("21_5e8ek")
ClientSyncScene = ExtResource("20_yvkm1")
image = ExtResource("22_a28cf")
id = 14
name = "CB1300"
price = 0
type = "Food"
in_hand = true
usable = false
remove_on_ban = false

[sub_resource type="Resource" id="Resource_1utvh"]
script = ExtResource("2_6c7y3")
VehicleScene = ExtResource("24_r7fdb")
ClientSyncScene = ExtResource("23_f3nyg")
image = ExtResource("25_rtk03")
id = 15
name = "Harley"
price = 0
type = "Food"
in_hand = true
usable = false
remove_on_ban = false

[sub_resource type="Resource" id="Resource_1hyfa"]
script = ExtResource("2_6c7y3")
VehicleScene = ExtResource("27_x8s1l")
ClientSyncScene = ExtResource("26_82ig3")
image = ExtResource("28_w1qdh")
id = 16
name = "3Weel"
price = 0
type = "Food"
in_hand = true
usable = false
remove_on_ban = false

[sub_resource type="Resource" id="Resource_tfvyt"]
script = ExtResource("2_6c7y3")
VehicleScene = ExtResource("30_misr5")
ClientSyncScene = ExtResource("29_b6f2d")
image = ExtResource("31_qn85b")
id = 17
name = "Forghoon"
price = 0
type = "Food"
in_hand = true
usable = false
remove_on_ban = false

[node name="VehicleManager" type="Node" groups=["OnExitFreeride", "OnReconnect"]]
script = ExtResource("1_v6q8o")
vehicle_items = Array[ExtResource("2_6c7y3")]([ExtResource("3_xoj10"), ExtResource("4_arond"), ExtResource("6_v5jli"), ExtResource("5_xcc8q"), ExtResource("7_g8rxc"), ExtResource("8_bxwls"), ExtResource("9_lj8mm"), ExtResource("10_0m2lb"), ExtResource("11_yunsi"), ExtResource("12_3uqkv"), ExtResource("13_uljah"), SubResource("Resource_js4fd"), SubResource("Resource_6qgqn"), SubResource("Resource_vdw4y"), SubResource("Resource_1utvh"), SubResource("Resource_1hyfa"), SubResource("Resource_tfvyt")])
