extends Vehicle
class_name MyVehicle

var NetworkID: int#1 byte id
var TypeID: int
var OwnerID: int
var is_locked = false
var light = false

@onready var sits_parent: Node3D = $SitsParent
@onready var exit_pos: Marker3D = $ExitPos
@onready var camera_look: Marker3D = $CameraLook
@onready var hud: ExitableControl = $HUD
@onready var vcontainer: VBoxContainer = $HUD/Panel/VBoxContainer
@onready var crash_area: Area3D = $CrashArea

@export_group("Horn")
@export var has_horn = true
@export var horn_type: SoundManager.SOUND_TYPE = SoundManager.SOUND_TYPE.Horn4
@export var horn_distance = 60.0
@export_group("Stunt")
@export var has_stunt = false


const SIT_BUTTON = preload("res://Scenes/Vehicle/Scenes/SitButton.tscn")
var should_stop = false

var freeze_counter = 0
var freeze_time = 1.0#sec
var is_freeze = false

func _ready() -> void:
	super()
	exit_hud()
	var index = 0
	for sit:VehicleSit in sits_parent.get_children():
		sit.index = index
		index += 1
		
		var btn = SIT_BUTTON.instantiate()
		vcontainer.add_child(btn)
		btn.set_sit(sit)
		btn.pressed.connect(on_sit_button_pressed)


func _process(_delta: float) -> void:
	return


func _physics_process(delta: float) -> void:
	if is_freeze:
		linear_velocity = Vector3()
		angular_velocity = Vector3()
		freeze_counter += delta
		if freeze_counter > freeze_time:
			is_freeze = false
		super(delta)
		return
		
	super(delta)
	if Constants.is_client():
		var client = Constants.client
		if not is_instance_valid(client):
			return
		
		if not has_driver() and get_wheel_contact_count() > 0:
			if speed_kmh() < 10:
				linear_velocity = Vector3(0, 0, 0)
		
		if should_stop and get_wheel_contact_count() > 0:
			linear_velocity = Vector3(0, 0, 0)


func start_gas():
	if current_gear == -1:
		brake_input = 1
	else:
		throttle_input = 1


func release_gas():
	if current_gear == -1:
		brake_input = 0
	else:
		throttle_input = 0


func on_right(_delta):
	steering_input = -1


func on_left(_delta):
	steering_input = 1


func release_steer(_delta):
	steering_input = 0


func start_brake():
	if current_gear == -1:
		throttle_input = 1
	else:
		brake_input = 1


func release_brake():
	if current_gear == -1:
		throttle_input = 0
	else:
		brake_input = 0


func data_size():
	return 24 + 12


#18-19-20-21-22-23 (6 Bytes) Are empty and can set for each vehicle!
func encode(buffer:PackedByteArray) -> PackedByteArray:
	var start_index = buffer.size()
	buffer.resize(buffer.size() + data_size())
	
	buffer.encode_u8(start_index, NetworkID)#1Byte
	buffer.encode_u8(start_index + 1, TypeID)#1Byte
	buffer.encode_u32(start_index + 2, OwnerID)#1Byte
	buffer.encode_half(start_index + 6, global_position.x) #2bytes
	buffer.encode_half(start_index + 8, global_position.y) #2bytes
	buffer.encode_half(start_index + 10, global_position.z) #2bytes
	
	buffer.encode_half(start_index + 12, global_rotation.x) #2bytes
	buffer.encode_half(start_index + 14, rad_to_deg(global_rotation.y)) #2bytes
	buffer.encode_half(start_index + 16, global_rotation.z) #2bytes
	
	#buffer.encode_half(start_index + 18, linear_velocity.x) #2bytes
	#buffer.encode_half(start_index + 20, linear_velocity.y) #2bytes
	#buffer.encode_half(start_index + 22, linear_velocity.z) #2bytes
	
	#DriverMode
	buffer.encode_half(start_index + 24, network_steering)
	buffer.encode_u8(start_index + 26, int(network_brake))
	buffer.encode_u16(start_index + 27, int(network_rpm))
	
	buffer.encode_half(start_index + 29, network_velocity.x)
	buffer.encode_half(start_index + 31, network_velocity.y)
	buffer.encode_half(start_index + 33, network_velocity.z)
	
	buffer.encode_u8(start_index + 35, network_light)
	
	
	return buffer


func decode(start_index:int, buffer:PackedByteArray) -> int:
	#Just For Spawn In VehicleManager
	#Not Using. Use ClientSyncVehicle Instead
	
	NetworkID = buffer.decode_u8(start_index)
	TypeID = buffer.decode_u8(start_index + 1)
	OwnerID = buffer.decode_u32(start_index + 2)
	
	global_position.x = buffer.decode_half(start_index + 6) #2bytes
	global_position.y = buffer.decode_half(start_index + 8) #2bytes
	global_position.z = buffer.decode_half(start_index + 10) #2bytes
	
	global_rotation.x = buffer.decode_half(start_index + 12) #2bytes
	global_rotation.y = deg_to_rad(buffer.decode_half(start_index + 14)) #2bytes
	global_rotation.z = buffer.decode_half(start_index + 16) #2bytes
	
	return data_size()


var last_action_time = 0
func _on_base_actionable_action() -> void:
	if Selector.in_prison:
		return
	if Time.get_ticks_msec() - last_action_time < 500:
		return
	last_action_time = Time.get_ticks_msec()
	hud.visible = true
	hud.process_mode = Node.PROCESS_MODE_INHERIT
	var freeride = Constants.client.game_scene as FreeRide
	hud.reparent(freeride.hud)


func is_sit_empty(sit_index):
	if sit_index < 0 or sit_index > sits_parent.get_child_count():
		return false
	
	return sits_parent.get_child(sit_index).is_empty()


func get_sit(sit_index) -> VehicleSit:
	if sit_index < 0 or sit_index > sits_parent.get_child_count():
		return null
	
	return sits_parent.get_child(sit_index)


func get_exit_pos() -> Vector3:
	return exit_pos.global_position


func unmount_player(player_id) -> bool:
	for sit:VehicleSit in sits_parent.get_children():
		if sit.playerId == player_id:
			sit.unmount_player()
			return true
	
	return false


func speed_kmh():
	return -(linear_velocity * transform.basis).z * 3.6


func has_driver():
	for sit:VehicleSit in sits_parent.get_children():
		if sit.is_driver:
			return not sit.is_empty()

	return false


func get_driver_sit() -> VehicleSit:
	for sit:VehicleSit in sits_parent.get_children():
		if sit.is_driver:
			return sit

	return null


func driver_mode_size():
	return 24


func encode_driver_mode(buffer:PackedByteArray) -> PackedByteArray:
	var start_index = buffer.size()
	buffer.resize(buffer.size() + driver_mode_size())
	
	buffer.encode_half(start_index, steering_amount)
	buffer.encode_u8(start_index + 2, int(brake_input))
	buffer.encode_u16(start_index + 3, int(motor_rpm))
	
	buffer.encode_half(start_index + 5, linear_velocity.x)
	buffer.encode_half(start_index + 7, linear_velocity.y)
	buffer.encode_half(start_index + 9, linear_velocity.z)
	
	buffer.encode_u8(start_index + 11, int(light))
	
	buffer.encode_half(start_index + 12, global_position.x) #2bytes
	buffer.encode_half(start_index + 14, global_position.y) #2bytes
	buffer.encode_half(start_index + 16, global_position.z) #2bytes
	
	buffer.encode_half(start_index + 18, global_rotation.x) #2bytes
	buffer.encode_half(start_index + 20, rad_to_deg(global_rotation.y)) #2bytes
	buffer.encode_half(start_index + 22, global_rotation.z) #2bytes
	
	return buffer


var network_steering = 0
var network_brake = 0
var network_rpm = 0
var network_velocity = Vector3()
var network_light = 0

#Runs in server
func decode_driver_mode(start_index:int, buffer:PackedByteArray) -> int:
	network_steering = buffer.decode_half(start_index)
	network_brake = buffer.decode_u8(start_index + 2)
	network_rpm = buffer.decode_u16(start_index + 3)
	
	network_velocity.x = buffer.decode_half(start_index + 5)
	network_velocity.y = buffer.decode_half(start_index + 7)
	network_velocity.z = buffer.decode_half(start_index + 9)
	
	network_light = buffer.decode_u8(start_index + 11)
	
	global_position.x = buffer.decode_half(start_index + 12) #2bytes
	global_position.y = buffer.decode_half(start_index + 14) #2bytes
	global_position.z = buffer.decode_half(start_index + 16) #2bytes
	
	global_rotation.x = buffer.decode_half(start_index + 18) #2bytes
	global_rotation.y = deg_to_rad(buffer.decode_half(start_index + 20)) #2bytes
	global_rotation.z = buffer.decode_half(start_index + 22) #2bytes
	
	return driver_mode_size()


#Should implement in each vehicle
func handle_cosmetics_state():
	pass


func exit_hud():
	hud.reparent(self)
	hud.visible = false
	hud.process_mode = Node.PROCESS_MODE_DISABLED


func on_sit_button_pressed(sit_index):
	if Selector.in_prison or Selector.am_i_sit:
		exit_hud()
		return
	VehicleManager.mount_vehicle.rpc_id(1, NetworkID, sit_index)
	exit_hud()


var last_solid_time = 0
func _on_crash_area_body_entered(body: Node3D) -> void:
	if Constants.is_server:
		return

	if body is MyVehicle or body is ClientSyncVehicle:
		on_solid_crash()
		return

	if body is Character:
		on_character_crash(body)
		return
	
	#Solid
	on_solid_crash()


func on_solid_crash():
	if Time.get_ticks_msec() - last_solid_time < 1000:
		return
	
	var absspeed = abs(speed_kmh())
	if absspeed < 30:
		return
	
	last_solid_time = Time.get_ticks_msec()
	VehicleManager.crash_solid.rpc_id(1, absspeed, linear_velocity)


func on_character_crash(character:Character):
	for sit:VehicleSit in sits_parent.get_children():
		if sit.playerId == character.player_id:
			return

	var absspeed = abs(speed_kmh())
	if absspeed < 30:
		return
	
	VehicleManager.crash_character.rpc_id(1, absspeed, character.player_id, linear_velocity)


func on_horn():
	SoundManager.play_3d_sound.rpc_id(1, global_position, horn_distance, horn_type)


func on_stunt():
	pass


func freeze(time):
	freeze_time = time
	is_freeze = true
	freeze_counter = 0
	previous_global_position = global_position
	local_velocity = Vector3()
	speed = 0
