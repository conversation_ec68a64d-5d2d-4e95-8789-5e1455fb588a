extends AudioStreamPlayer3D
class_name ClientEngineSound

@export var vehicle : ClientSyncVehicle
@export var sample_rpm := 4000.0
@export var disabled = false

func _ready() -> void:
	if disabled:
		stop()


func _physics_process(_delta):
	if Constants.is_server:
		return
		
	if vehicle.network_rpm == 0:
		volume_db = linear_to_db(0)
	else:
		pitch_scale = vehicle.network_rpm / sample_rpm
		var vol = 1
		vol *= DataSaver.get_item("sound_volume")
		volume_db = linear_to_db(vol)
