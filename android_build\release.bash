#!/bin/bash
set -eu

Version="1.27.2Release"

BuildConfigs=("IPG V8" "IPG V7" "Android Myket V7" "Android Myket V8" "Android NoCache")
#BuildConfigs=("IPG V8" "IPG V7")
Command=""

length=${#BuildConfigs[@]}

for (( i=0; i<${length}; i++ ));
do
	echo ${BuildConfigs[$i]}
	c=${BuildConfigs[$i]}
	Command="$Command"'&&godot --headless -v --export-release "'"$c"'" "/root/output/'"$c"'.apk"'
done

#Bazaar aab
Command="$Command"'&&godot --headless -v --export-release "Android Cafebazaar AAB" "/root/output/Android Cafebazaar AAB.aab"'


Command=${Command:2}

echo $Command

sudo docker run -v /home/<USER>/animal-rush-game:/root/godot -v /tmp:/root/output --network=host -e "GODOT_ANDROID_KEYSTORE_RELEASE_USER=mygame" -e "GODOT_ANDROID_KEYSTORE_RELEASE_PASSWORD=zidaneronaldo" -e "GODOT_ANDROID_KEYSTORE_RELEASE_PATH=/root/godot/mygame.keystore" godot-builder:4.3 /bin/bash -c ''"$Command"

echo $Command

echo "Uploading..."
source /home/<USER>/animal-rush-game/android_build/.venv/bin/activate
for (( i=0; i<${length}; i++ ));
do
        c=${BuildConfigs[$i]}
	time python /home/<USER>/animal-rush-game/android_build/uploader.py "/tmp/$c.apk" "$Version-$c.apk"
done

#Bazaar aab
time python /home/<USER>/animal-rush-game/android_build/uploader.py "/tmp/Android Cafebazaar AAB.aab" "$Version-bazaar.aab"

#CleanUp Docker containers
sudo docker rm -v $(sudo  docker ps --filter status=exited -q)

