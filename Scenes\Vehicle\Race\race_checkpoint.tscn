[gd_scene load_steps=8 format=4 uid="uid://cm6yiol17u42a"]

[ext_resource type="Script" path="res://Scenes/Vehicle/Race/RaceCheckpoint.gd" id="1_d0b4k"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_qqccd"]
resource_name = "White"
cull_mode = 2
albedo_color = Color(0.94854, 0.94854, 0.94854, 1)
roughness = 0.626181

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_dfw51"]
resource_name = "Blue"
cull_mode = 2
albedo_color = Color(0.209508, 0.380054, 0.709432, 1)
roughness = 0.690401

[sub_resource type="ArrayMesh" id="ArrayMesh_lm8xp"]
_surfaces = [{
"aabb": AABB(-60.3318, -782.267, -559.334, 120.665, 1564.56, 1154.79),
"format": 34359742465,
"index_count": 150,
"index_data": PackedByteArray("AAACAAEAAAADAAIABQAGAAQABQAHAAYACQAKAAgACQALAAoADQAOAAwADQAPAA4AEQASABAAEQATABIAFQAWABQAFQAXABYAGAAaABkAGAAbABoAHAAfAB4AHAAdAB8AIAAjACIAIAAhACMAJAAnACYAJAAlACcAKAArACoAKAApACsALAAvAC4ALAAtAC8AGAADAAAAGAAZAAMAGgABAAIAGgAbAAEAHAAHAAUAHAAeAAcAHwAEAAYAHwAdAAQAIAALAAkAIAAiAAsAIgAKAAsAIgAjAAoAIwAIAAoAIwAhAAgAJAAPAA0AJAAmAA8AJwAMAA4AJwAlAAwAKAATABEAKAAqABMAKwAQABIAKwApABAALAAXABUALAAuABcALwAUABYALwAtABQA"),
"name": "White",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 48,
"vertex_data": PackedByteArray("AFVxQoBNZMPqR8LDwFNxwoBNZMPqR8LDwFNxwgCr1sP+7YXDAFVxQgCr1sP+7YXDwFNxwoC1EMRoJ6XCAFVxQoC1EMRoJ6XCwFNxwhChJMR6FRFDAFVxQhChJMR6FRFDwFNxwhChJMSywcFDAFVxQhChJMSywcFDwFNxwmDOFMRv3RREAFVxQmDOFMRv3RREwFNxwuCiJESywcFDAFVxQuCiJESywcFDwFNxwuCiJESQFRFDAFVxQuCiJESQFRFDwFNxwkC3EERAJ6XCAFVxQkC3EERAJ6XCwFNxwoCu1kP67YXDAFVxQoCu1kP67YXDwFNxwkBUZEPmR8LDAFVxQkBUZEPmR8LDwFNxwgAAYDzKNtfDAFVxQgAAYDzKNtfDAFVxQqDVh8N6xf7DAFVxQsBK/8MuFrfDwFNxwsBK/8MuFrfDwFNxwqDVh8N6xf7DAFVxQlD6K8SkhBLDwFNxwlD6K8SkhBLDAFVxQhCRQ8SI2/VCwFNxwhCRQ8SI2/VCAFVxQhCRQ8SO1cxDwFNxwhCRQ8SO1cxDAFVxQpDNMsR33RREwFNxwpDNMsR33RREAFVxQtCSQ0SO1cxDwFNxwtCSQ0SO1cxDAFVxQtCSQ0Sw2/VCwFNxwtCSQ0Sw2/VCAFVxQgD8K0SQhBLDwFNxwgD8K0SQhBLDAFVxQiBO/0MmFrfDwFNxwiBO/0MmFrfDAFVxQiDZh0N6xf7DwFNxwiDZh0N6xf7DAFVxQgAAYDxn1QvEwFNxwgAAYDxn1QvE")
}, {
"aabb": AABB(-60.3318, -782.267, -559.334, 120.665, 1564.56, 1154.79),
"format": 34359742465,
"index_count": 150,
"index_data": PackedByteArray("AwAEAAIAAwAFAAQABwAIAAYABwAJAAgACgAMAAsACgANAAwADwAQAA4ADwARABAAEwAUABIAEwAVABQAFwABABYAFwAAAAEAGQAdABwAGQAaAB0AHgAhACAAHgAfACEAIgAkACMAIgAlACQAJgApACgAJgAnACkAKgAtACwAKgArAC0ALgAbABgALgAvABsAGQAFAAMAGQAcAAUAHQACAAQAHQAaAAIAHgAJAAcAHgAgAAkAIQAGAAgAIQAfAAYAIgANAAoAIgAjAA0AJAALAAwAJAAlAAsAJQAKAAsAJQAiAAoAJgARAA8AJgAoABEAKQAOABAAKQAnAA4AKgAVABMAKgAsABUALQASABQALQArABIALgAAABcALgAYAAAAGwAWAAEAGwAvABYA"),
"name": "Blue",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 48,
"vertex_data": PackedByteArray("AFVxQoBNZMPqR8LDwFNxwoBNZMPqR8LDwFNxwgCr1sP+7YXDAFVxQgCr1sP+7YXDwFNxwoC1EMRoJ6XCAFVxQoC1EMRoJ6XCwFNxwhChJMR6FRFDAFVxQhChJMR6FRFDwFNxwhChJMSywcFDAFVxQhChJMSywcFDAFVxQsDEFERv3RREwFNxwsDEFERv3RREwFNxwuCiJESywcFDAFVxQuCiJESywcFDwFNxwuCiJESQFRFDAFVxQuCiJESQFRFDwFNxwkC3EERAJ6XCAFVxQkC3EERAJ6XCwFNxwoCu1kP67YXDAFVxQoCu1kP67YXDwFNxwkBUZEPmR8LDAFVxQkBUZEPmR8LDwFNxwgAAYDzKNtfDAFVxQgAAYDzKNtfDAFVxQqDVh8N6xf7DAFVxQsBK/8MuFrfDwFNxwsBK/8MuFrfDwFNxwqDVh8N6xf7DAFVxQlD6K8SkhBLDwFNxwlD6K8SkhBLDAFVxQhCRQ8SI2/VCwFNxwhCRQ8SI2/VCAFVxQhCRQ8SO1cxDwFNxwhCRQ8SO1cxDAFVxQvDDMkR33RREAFVxQtCSQ0SO1cxDwFNxwtCSQ0SO1cxDwFNxwvDDMkR33RREAFVxQtCSQ0Sw2/VCwFNxwtCSQ0Sw2/VCAFVxQgD8K0SQhBLDwFNxwgD8K0SQhBLDAFVxQiBO/0MmFrfDwFNxwiBO/0MmFrfDAFVxQiDZh0N6xf7DwFNxwiDZh0N6xf7DAFVxQgAAYDxn1QvEwFNxwgAAYDxn1QvE")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_a0dbr"]
resource_name = "Ramp_checkpointglb_Mesh_005"
_surfaces = [{
"aabb": AABB(-60.3318, -782.267, -559.334, 120.665, 1564.56, 1154.79),
"attribute_data": PackedByteArray("AADAPgAAMD8AAMA+AAAwPwAAwD4AAKA+AADAPgAAoD5yHMc+AACgPnIcxz4AAKA+chzHPgAAMD9yHMc+AAAwP+Q4zj4AAKA+5DjOPgAAoD7kOM4+AAAwP+Q4zj4AADA/VlXVPgAAoD5WVdU+AACgPlZV1T4AADA/VlXVPgAAMD/Icdw+AACgPshx3D4AAKA+yHHcPgAAMD/Icdw+AAAwPzqO4z4AAKA+Oo7jPgAAoD46juM+AACgPjqO4z4AADA/Oo7jPgAAMD86juM+AAAwP6yqCj8AAKA+rKoKPwAAoD6sqgo/AAAwP6yqCj8AADA/5TgOPwAAoD7lOA4/AACgPuU4Dj8AADA/5TgOPwAAMD8exxE/AACgPh7HET8AAKA+HscRPwAAMD8exxE/AAAwP1dVFT8AAKA+V1UVPwAAoD5XVRU/AAAwP1dVFT8AADA/kOMYPwAAoD6Q4xg/AACgPpDjGD8AADA/kOMYPwAAMD/JcRw/AACgPslxHD8AAKA+yXEcPwAAMD/JcRw/AAAwPwAAwD4AADA/AADAPgAAMD9yHMc+AAAwP3Icxz4AADA/chzHPgAAoD5yHMc+AACgPgAAwD4AAKA+AADAPgAAoD7kOM4+AAAwP+Q4zj4AADA/5DjOPgAAoD7kOM4+AACgPlZV1T4AADA/VlXVPgAAMD9WVdU+AACgPlZV1T4AAKA+yHHcPgAAMD/Icdw+AAAwP8hx3D4AAKA+yHHcPgAAoD46juM+AAAwPzqO4z4AADA/Oo7jPgAAMD86juM+AACgPjqO4z4AAKA+Oo7jPgAAoD6sqgo/AAAwP6yqCj8AADA/rKoKPwAAoD6sqgo/AACgPuU4Dj8AADA/5TgOPwAAMD/lOA4/AACgPuU4Dj8AAKA+HscRPwAAMD8exxE/AAAwPx7HET8AAKA+HscRPwAAoD5XVRU/AAAwP1dVFT8AADA/V1UVPwAAoD5XVRU/AACgPpDjGD8AADA/kOMYPwAAMD+Q4xg/AACgPpDjGD8AAKA+yXEcPwAAMD/JcRw/AAAwP8lxHD8AAKA+yXEcPwAAoD4="),
"format": 34359742487,
"index_count": 150,
"index_data": PackedByteArray("AAAEAAIAAAAGAAQACgAMAAgACgAOAAwAEgAVABAAEgAYABUAHAAeABoAHAAgAB4AJAAmACIAJAAoACYALAAuACoALAAwAC4AMgA2ADQAMgA4ADYAOgBAAD4AOgA8AEAAQgBKAEcAQgBEAEoATABSAFAATABOAFIAVABaAFgAVABWAFoAXABiAGAAXABeAGIAMwAHAAEAMwA1AAcANwADAAUANwA5AAMAOwAPAAsAOwA/AA8AQQAJAA0AQQA9AAkAQwAZABMAQwBIABkARgAUABcARgBJABQASwARABYASwBFABEATQAhAB0ATQBRACEAUwAbAB8AUwBPABsAVQApACUAVQBZACkAWwAjACcAWwBXACMAXQAxAC0AXQBhADEAYwArAC8AYwBfACsA"),
"material": SubResource("StandardMaterial3D_qqccd"),
"name": "White",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 100,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-60.3318, -782.267, -559.334, 120.665, 1564.56, 1154.79),
"attribute_data": PackedByteArray("AgAgPwAAMD8CACA/AAAwPwIAID8AAKA+AgAgPwAAoD5yHMc+AACgPnIcxz4AAKA+chzHPgAAMD9yHMc+AAAwP+Q4zj4AAKA+5DjOPgAAoD7kOM4+AAAwP+Q4zj4AADA/VlXVPgAAoD5WVdU+AACgPlZV1T4AADA/VlXVPgAAMD/Icdw+AACgPshx3D4AAKA+yHHcPgAAMD/Icdw+AAAwP3McBz8AADA/cxwHPwAAMD9zHAc/AAAwP3McBz8AAKA+cxwHPwAAoD5zHAc/AACgPqyqCj8AAKA+rKoKPwAAoD6sqgo/AAAwP6yqCj8AADA/5TgOPwAAoD7lOA4/AACgPuU4Dj8AADA/5TgOPwAAMD8exxE/AACgPh7HET8AAKA+HscRPwAAMD8exxE/AAAwP1dVFT8AAKA+V1UVPwAAoD5XVRU/AAAwP1dVFT8AADA/kOMYPwAAoD6Q4xg/AACgPpDjGD8AADA/kOMYPwAAMD/JcRw/AACgPslxHD8AAKA+yXEcPwAAMD/JcRw/AAAwPwIAID8AADA/AgAgPwAAMD9yHMc+AAAwP3Icxz4AADA/chzHPgAAoD5yHMc+AACgPgIAID8AAKA+AgAgPwAAoD7kOM4+AAAwP+Q4zj4AADA/5DjOPgAAoD7kOM4+AACgPlZV1T4AADA/VlXVPgAAMD9WVdU+AACgPlZV1T4AAKA+yHHcPgAAMD/Icdw+AAAwP8hx3D4AAKA+yHHcPgAAoD5zHAc/AAAwP3McBz8AADA/cxwHPwAAMD+sqgo/AAAwP6yqCj8AADA/rKoKPwAAoD6sqgo/AACgPnMcBz8AAKA+cxwHPwAAoD5zHAc/AACgPuU4Dj8AADA/5TgOPwAAMD/lOA4/AACgPuU4Dj8AAKA+HscRPwAAMD8exxE/AAAwPx7HET8AAKA+HscRPwAAoD5XVRU/AAAwP1dVFT8AADA/V1UVPwAAoD5XVRU/AACgPpDjGD8AADA/kOMYPwAAMD+Q4xg/AACgPpDjGD8AAKA+yXEcPwAAMD/JcRw/AAAwP8lxHD8AAKA+yXEcPwAAoD4="),
"format": 34359742487,
"index_count": 150,
"index_data": PackedByteArray("BgAIAAQABgAKAAgADgAQAAwADgASABAAFQAaABgAFQAcABoAIAAiAB4AIAAkACIAKAAqACYAKAAsACoAMAACAC4AMAAAAAIANAA8ADoANAA2ADwAPgBEAEIAPgBAAEQARwBLAEkARwBOAEsAUABWAFQAUABSAFYAWABeAFwAWABaAF4AYAA4ADIAYABiADgANQALAAcANQA7AAsAPQAFAAkAPQA3AAUAPwATAA8APwBDABMARQANABEARQBBAA0ASAAdABYASABKAB0ATAAZABsATABPABkATQAUABcATQBGABQAUQAlACEAUQBVACUAVwAfACMAVwBTAB8AWQAtACkAWQBdAC0AXwAnACsAXwBbACcAYQABADEAYQAzAAEAOQAvAAMAOQBjAC8A"),
"material": SubResource("StandardMaterial3D_dfw51"),
"name": "Blue",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 100,
"vertex_data": PackedByteArray("AFVxQoBNZMPqR8LDAFVxQoBNZMPqR8LDwFNxwoBNZMPqR8LDwFNxwoBNZMPqR8LDwFNxwgCr1sP+7YXDwFNxwgCr1sP+7YXDAFVxQgCr1sP+7YXDAFVxQgCr1sP+7YXDwFNxwoC1EMRoJ6XCwFNxwoC1EMRoJ6XCAFVxQoC1EMRoJ6XCAFVxQoC1EMRoJ6XCwFNxwhChJMR6FRFDwFNxwhChJMR6FRFDAFVxQhChJMR6FRFDAFVxQhChJMR6FRFDwFNxwhChJMSywcFDwFNxwhChJMSywcFDAFVxQhChJMSywcFDAFVxQhChJMSywcFDAFVxQsDEFERv3RREAFVxQsDEFERv3RREAFVxQsDEFERv3RREwFNxwsDEFERv3RREwFNxwsDEFERv3RREwFNxwsDEFERv3RREwFNxwuCiJESywcFDwFNxwuCiJESywcFDAFVxQuCiJESywcFDAFVxQuCiJESywcFDwFNxwuCiJESQFRFDwFNxwuCiJESQFRFDAFVxQuCiJESQFRFDAFVxQuCiJESQFRFDwFNxwkC3EERAJ6XCwFNxwkC3EERAJ6XCAFVxQkC3EERAJ6XCAFVxQkC3EERAJ6XCwFNxwoCu1kP67YXDwFNxwoCu1kP67YXDAFVxQoCu1kP67YXDAFVxQoCu1kP67YXDwFNxwkBUZEPmR8LDwFNxwkBUZEPmR8LDAFVxQkBUZEPmR8LDAFVxQkBUZEPmR8LDwFNxwgAAYDzKNtfDwFNxwgAAYDzKNtfDAFVxQgAAYDzKNtfDAFVxQgAAYDzKNtfDAFVxQqDVh8N6xf7DAFVxQqDVh8N6xf7DAFVxQsBK/8MuFrfDAFVxQsBK/8MuFrfDwFNxwsBK/8MuFrfDwFNxwsBK/8MuFrfDwFNxwqDVh8N6xf7DwFNxwqDVh8N6xf7DAFVxQlD6K8SkhBLDAFVxQlD6K8SkhBLDwFNxwlD6K8SkhBLDwFNxwlD6K8SkhBLDAFVxQhCRQ8SI2/VCAFVxQhCRQ8SI2/VCwFNxwhCRQ8SI2/VCwFNxwhCRQ8SI2/VCAFVxQhCRQ8SO1cxDAFVxQhCRQ8SO1cxDwFNxwhCRQ8SO1cxDwFNxwhCRQ8SO1cxDAFVxQvDDMkR33RREAFVxQvDDMkR33RREAFVxQvDDMkR33RREAFVxQtCSQ0SO1cxDAFVxQtCSQ0SO1cxDwFNxwtCSQ0SO1cxDwFNxwtCSQ0SO1cxDwFNxwvDDMkR33RREwFNxwvDDMkR33RREwFNxwvDDMkR33RREAFVxQtCSQ0Sw2/VCAFVxQtCSQ0Sw2/VCwFNxwtCSQ0Sw2/VCwFNxwtCSQ0Sw2/VCAFVxQgD8K0SQhBLDAFVxQgD8K0SQhBLDwFNxwgD8K0SQhBLDwFNxwgD8K0SQhBLDAFVxQiBO/0MmFrfDAFVxQiBO/0MmFrfDwFNxwiBO/0MmFrfDwFNxwiBO/0MmFrfDAFVxQiDZh0N6xf7DAFVxQiDZh0N6xf7DwFNxwiDZh0N6xf7DwFNxwiDZh0N6xf7DAFVxQgAAYDxn1QvEAFVxQgAAYDxn1QvEwFNxwgAAYDxn1QvEwFNxwgAAYDxn1QvE/38lo/9/bG7///9/////v/9/JaP/f2xuAAD/f////7//f6a7/38rYgAA/3////+//3+mu/9/K2L///9/////v/9/UdL/f9ZWAAD/f////7//f1HS/3/WVv///3////+//39o7f9/S0kAAP9/////v/9/aO3/f0tJ////f////7/Fj////38cOAAA/3////+/xY////9/HDj///9/////v/9//3////8/7Z0AABDiAAD///9/////v/9//3////8/7Z0AABDiAAAAAP9/////v82PAAAw8AAAAAD/f////7/NjwAAMPAAAP///3////+//3+PEm/t/n8AAP9/////v/9/jxJv7f5/////f////7//f7EtTdL+fwAA/3////+//3+xLU3S/n////9/////v/9/WESmu/5/AAD/f////7//f1hEprv+f////3////+//3/WXCij/n8AAP9/////v/9/1lwoo/5/////f////7//f/9//3/+fwAA/3////+//3//f/9//n////9/////v93cAAD/f5CR////f////79fxAAA/3/Pnf///3////+/X8QAAP9/z50AAP9/////v93cAAD/f5CRAAD/f////7+1rQAA/38kqf///3////+/ta0AAP9/JKkAAP9/////v5iSAAD/f7K2////f////7+YkgAA/3+ytgAA/3////+//3/oD/9/88f///9/////v/9/6A//f/PHAAD/f////7//f/9/////P/9/+9373f//////f////7//fwzwDPD//////3////+//38M8Azw//8AAP9/////v/9//3////8//3/73fvd//8AAP9/////v5GS//9t7QCA////f////7+Rkv//be0AgAAA/3////+/ta3//0nSAID///9/////v7Wt//9J0gCAAAD/f////79fxP//nrsAgP///3////+/X8T//567AIAAAP9/////v93c//8gowCA////f////7/d3P//IKMAgAAA/3////+///////9/AID///9/////v///////fwCAAAD/f////78=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_lm8xp")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_tkch1"]
data = PackedVector3Array(60.333, -228.303, -388.562, -60.3318, -429.336, -267.859, -60.3318, -228.303, -388.562, 60.333, -228.303, -388.562, 60.333, -429.336, -267.859, -60.3318, -429.336, -267.859, 60.333, -578.836, -82.577, -60.3318, -658.517, 145.084, -60.3318, -578.836, -82.577, 60.333, -578.836, -82.577, 60.333, -658.517, 145.084, -60.3318, -658.517, 145.084, 60.333, -658.517, 387.513, -60.3318, -595.225, 595.46, -60.3318, -658.517, 387.513, 60.333, -658.517, 387.513, 60.333, -595.225, 595.46, -60.3318, -595.225, 595.46, 60.333, 658.545, 387.513, -60.3318, 658.545, 145.084, -60.3318, 658.545, 387.513, 60.333, 658.545, 387.513, 60.333, 658.545, 145.084, -60.3318, 658.545, 145.084, 60.333, 578.863, -82.5767, -60.3318, 429.363, -267.859, -60.3318, 578.863, -82.5767, 60.333, 578.863, -82.5767, 60.333, 429.363, -267.859, -60.3318, 429.363, -267.859, 60.333, 228.329, -388.562, -60.3318, 0.0137, -430.428, -60.3318, 228.329, -388.562, 60.333, 228.329, -388.562, 60.333, 0.0137, -430.428, -60.3318, 0.0137, -430.428, 60.333, -271.669, -509.543, -60.3318, -510.584, -366.173, 60.333, -510.584, -366.173, 60.333, -271.669, -509.543, -60.3318, -271.669, -509.543, -60.3318, -510.584, -366.173, 60.333, -687.911, -146.518, -60.3318, -782.267, 122.929, 60.333, -782.267, 122.929, 60.333, -687.911, -146.518, -60.3318, -687.911, -146.518, -60.3318, -782.267, 122.929, 60.333, -782.267, 409.668, -60.3318, -715.212, 595.46, 60.333, -715.212, 595.46, 60.333, -782.267, 409.668, -60.3318, -782.267, 409.668, -60.3318, -715.212, 595.46, 60.333, 782.294, 409.668, -60.3318, 782.294, 122.929, 60.333, 782.294, 122.929, 60.333, 782.294, 409.668, -60.3318, 782.294, 409.668, -60.3318, 782.294, 122.929, 60.333, 687.938, -146.518, -60.3318, 510.61, -366.173, 60.333, 510.61, -366.173, 60.333, 687.938, -146.518, -60.3318, 687.938, -146.518, -60.3318, 510.61, -366.173, 60.333, 271.696, -509.543, -60.3318, 0.0137, -559.334, 60.333, 0.0137, -559.334, 60.333, 271.696, -509.543, -60.3318, 271.696, -509.543, -60.3318, 0.0137, -559.334, 60.333, -271.669, -509.543, 60.333, -429.336, -267.859, 60.333, -228.303, -388.562, 60.333, -271.669, -509.543, 60.333, -510.584, -366.173, 60.333, -429.336, -267.859, -60.3318, -510.584, -366.173, -60.3318, -228.303, -388.562, -60.3318, -429.336, -267.859, -60.3318, -510.584, -366.173, -60.3318, -271.669, -509.543, -60.3318, -228.303, -388.562, 60.333, -687.911, -146.518, 60.333, -658.517, 145.084, 60.333, -578.836, -82.577, 60.333, -687.911, -146.518, 60.333, -782.267, 122.929, 60.333, -658.517, 145.084, -60.3318, -782.267, 122.929, -60.3318, -578.836, -82.577, -60.3318, -658.517, 145.084, -60.3318, -782.267, 122.929, -60.3318, -687.911, -146.518, -60.3318, -578.836, -82.577, 60.333, -782.267, 409.668, 60.333, -595.225, 595.46, 60.333, -658.517, 387.513, 60.333, -782.267, 409.668, 60.333, -715.212, 595.46, 60.333, -595.225, 595.46, 60.333, -715.212, 595.46, -60.3318, -595.225, 595.46, 60.333, -595.225, 595.46, 60.333, -715.212, 595.46, -60.3318, -715.212, 595.46, -60.3318, -595.225, 595.46, -60.3318, -715.212, 595.46, -60.3318, -658.517, 387.513, -60.3318, -595.225, 595.46, -60.3318, -715.212, 595.46, -60.3318, -782.267, 409.668, -60.3318, -658.517, 387.513, 60.333, 782.294, 409.668, 60.333, 658.545, 145.084, 60.333, 658.545, 387.513, 60.333, 782.294, 409.668, 60.333, 782.294, 122.929, 60.333, 658.545, 145.084, -60.3318, 782.294, 122.929, -60.3318, 658.545, 387.513, -60.3318, 658.545, 145.084, -60.3318, 782.294, 122.929, -60.3318, 782.294, 409.668, -60.3318, 658.545, 387.513, 60.333, 687.938, -146.518, 60.333, 429.363, -267.859, 60.333, 578.863, -82.5767, 60.333, 687.938, -146.518, 60.333, 510.61, -366.173, 60.333, 429.363, -267.859, -60.3318, 510.61, -366.173, -60.3318, 578.863, -82.5767, -60.3318, 429.363, -267.859, -60.3318, 510.61, -366.173, -60.3318, 687.938, -146.518, -60.3318, 578.863, -82.5767, 60.333, 271.696, -509.543, 60.333, 0.0137, -430.428, 60.333, 228.329, -388.562, 60.333, 271.696, -509.543, 60.333, 0.0137, -559.334, 60.333, 0.0137, -430.428, -60.3318, 0.0137, -559.334, -60.3318, 228.329, -388.562, -60.3318, 0.0137, -430.428, -60.3318, 0.0137, -559.334, -60.3318, 271.696, -509.543, -60.3318, 228.329, -388.562, 60.333, -429.336, -267.859, -60.3318, -578.836, -82.577, -60.3318, -429.336, -267.859, 60.333, -429.336, -267.859, 60.333, -578.836, -82.577, -60.3318, -578.836, -82.577, 60.333, -658.517, 145.084, -60.3318, -658.517, 387.513, -60.3318, -658.517, 145.084, 60.333, -658.517, 145.084, 60.333, -658.517, 387.513, -60.3318, -658.517, 387.513, 60.333, 595.074, 595.46, -60.3318, 658.545, 387.513, -60.3318, 595.074, 595.46, 60.333, 595.074, 595.46, 60.333, 658.545, 387.513, -60.3318, 658.545, 387.513, 60.333, 658.545, 145.084, -60.3318, 578.863, -82.5767, -60.3318, 658.545, 145.084, 60.333, 658.545, 145.084, 60.333, 578.863, -82.5767, -60.3318, 578.863, -82.5767, 60.333, 429.363, -267.859, -60.3318, 228.329, -388.562, -60.3318, 429.363, -267.859, 60.333, 429.363, -267.859, 60.333, 228.329, -388.562, -60.3318, 228.329, -388.562, 60.333, 0.0137, -430.428, -60.3318, -228.303, -388.562, -60.3318, 0.0137, -430.428, 60.333, 0.0137, -430.428, 60.333, -228.303, -388.562, -60.3318, -228.303, -388.562, 60.333, -510.584, -366.173, -60.3318, -687.911, -146.518, 60.333, -687.911, -146.518, 60.333, -510.584, -366.173, -60.3318, -510.584, -366.173, -60.3318, -687.911, -146.518, 60.333, -782.267, 122.929, -60.3318, -782.267, 409.668, 60.333, -782.267, 409.668, 60.333, -782.267, 122.929, -60.3318, -782.267, 122.929, -60.3318, -782.267, 409.668, 60.333, 715.061, 595.46, -60.3318, 782.294, 409.668, 60.333, 782.294, 409.668, 60.333, 715.061, 595.46, -60.3318, 715.061, 595.46, -60.3318, 782.294, 409.668, 60.333, 782.294, 122.929, -60.3318, 687.938, -146.518, 60.333, 687.938, -146.518, 60.333, 782.294, 122.929, -60.3318, 782.294, 122.929, -60.3318, 687.938, -146.518, 60.333, 510.61, -366.173, -60.3318, 271.696, -509.543, 60.333, 271.696, -509.543, 60.333, 510.61, -366.173, -60.3318, 510.61, -366.173, -60.3318, 271.696, -509.543, 60.333, 0.0137, -559.334, -60.3318, -271.669, -509.543, 60.333, -271.669, -509.543, 60.333, 0.0137, -559.334, -60.3318, 0.0137, -559.334, -60.3318, -271.669, -509.543, 60.333, -510.584, -366.173, 60.333, -578.836, -82.577, 60.333, -429.336, -267.859, 60.333, -510.584, -366.173, 60.333, -687.911, -146.518, 60.333, -578.836, -82.577, -60.3318, -687.911, -146.518, -60.3318, -429.336, -267.859, -60.3318, -578.836, -82.577, -60.3318, -687.911, -146.518, -60.3318, -510.584, -366.173, -60.3318, -429.336, -267.859, 60.333, -782.267, 122.929, 60.333, -658.517, 387.513, 60.333, -658.517, 145.084, 60.333, -782.267, 122.929, 60.333, -782.267, 409.668, 60.333, -658.517, 387.513, -60.3318, -782.267, 409.668, -60.3318, -658.517, 145.084, -60.3318, -658.517, 387.513, -60.3318, -782.267, 409.668, -60.3318, -782.267, 122.929, -60.3318, -658.517, 145.084, 60.333, 715.061, 595.46, 60.333, 658.545, 387.513, 60.333, 595.074, 595.46, 60.333, 715.061, 595.46, 60.333, 782.294, 409.668, 60.333, 658.545, 387.513, -60.3318, 782.294, 409.668, -60.3318, 595.074, 595.46, -60.3318, 658.545, 387.513, -60.3318, 782.294, 409.668, -60.3318, 715.061, 595.46, -60.3318, 595.074, 595.46, -60.3318, 715.061, 595.46, 60.333, 595.074, 595.46, -60.3318, 595.074, 595.46, -60.3318, 715.061, 595.46, 60.333, 715.061, 595.46, 60.333, 595.074, 595.46, 60.333, 782.294, 122.929, 60.333, 578.863, -82.5767, 60.333, 658.545, 145.084, 60.333, 782.294, 122.929, 60.333, 687.938, -146.518, 60.333, 578.863, -82.5767, -60.3318, 687.938, -146.518, -60.3318, 658.545, 145.084, -60.3318, 578.863, -82.5767, -60.3318, 687.938, -146.518, -60.3318, 782.294, 122.929, -60.3318, 658.545, 145.084, 60.333, 510.61, -366.173, 60.333, 228.329, -388.562, 60.333, 429.363, -267.859, 60.333, 510.61, -366.173, 60.333, 271.696, -509.543, 60.333, 228.329, -388.562, -60.3318, 271.696, -509.543, -60.3318, 429.363, -267.859, -60.3318, 228.329, -388.562, -60.3318, 271.696, -509.543, -60.3318, 510.61, -366.173, -60.3318, 429.363, -267.859, 60.333, 0.0137, -559.334, 60.333, -228.303, -388.562, 60.333, 0.0137, -430.428, 60.333, 0.0137, -559.334, 60.333, -271.669, -509.543, 60.333, -228.303, -388.562, -60.3318, -271.669, -509.543, -60.3318, 0.0137, -430.428, -60.3318, -228.303, -388.562, -60.3318, -271.669, -509.543, -60.3318, 0.0137, -559.334, -60.3318, 0.0137, -430.428)

[sub_resource type="BoxShape3D" id="BoxShape3D_efrnv"]
size = Vector3(686.645, 1427.9, 1033)

[node name="RaceCheckpoint" type="MeshInstance3D" groups=["VisibleGroup2"]]
transform = Transform3D(0.01, 0, 0, 0, 0, -0.01, 0, 0.01, 0, 0, 6.253, 0)
mesh = SubResource("ArrayMesh_a0dbr")
skeleton = NodePath("")
script = ExtResource("1_d0b4k")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_tkch1")

[node name="Area3D" type="Area3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Area3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2.28164, -3.92442, 83.6052)
shape = SubResource("BoxShape3D_efrnv")

[connection signal="body_entered" from="Area3D" to="." method="_on_area_3d_body_entered"]
