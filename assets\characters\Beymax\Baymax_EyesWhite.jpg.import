[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://byr3vioqmsl0l"
path.s3tc="res://.godot/imported/Baymax_EyesWhite.jpg-77a8c0052dc6ba9519a6c48f601c95d9.s3tc.ctex"
path.etc2="res://.godot/imported/Baymax_EyesWhite.jpg-77a8c0052dc6ba9519a6c48f601c95d9.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "3c1d492a67c4d13a2f57bfc6fe54f25a"
}

[deps]

source_file="res://assets/characters/Beymax/Baymax_EyesWhite.jpg"
dest_files=["res://.godot/imported/Baymax_EyesWhite.jpg-77a8c0052dc6ba9519a6c48f601c95d9.s3tc.ctex", "res://.godot/imported/Baymax_EyesWhite.jpg-77a8c0052dc6ba9519a6c48f601c95d9.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
