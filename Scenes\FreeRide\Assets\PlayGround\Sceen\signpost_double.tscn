[gd_scene load_steps=7 format=4 uid="uid://ibh4h46im4ej"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_tl4g3"]
cull_mode = 1
albedo_color = Color(0, 0, 0, 1)
grow = true
grow_amount = 0.015

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_lj8wk"]
next_pass = SubResource("StandardMaterial3D_tl4g3")
cull_mode = 2
diffuse_mode = 3
specular_mode = 1
albedo_color = Color(0.572549, 0.294118, 0.188235, 1)
roughness = 0.5

[sub_resource type="ArrayMesh" id="ArrayMesh_tnp6p"]
_surfaces = [{
"aabb": AABB(-0.898218, -1.08717e-08, -0.199823, 1.71298, 3.68064, 0.343833),
"format": 34359742465,
"index_count": 252,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACQAIAAQABAAGAAkADAAKAAsACwANAAwACwAOAA8ADwANAAsABQAAAAIAAgAHAAUAEgAQABEAEQATABIAEgAUABUAFQAQABIADAAWABcACgAMABcACgAXABgAFQAKABgAFQAYABEAEQAQABUACgAVABQAFAALAAoAFAAOAAsADgAUABIAEgAIAA4AEwAIABIACAAJAA4ACQAZAA4AGQAPAA4AHAAaABsAGwAdABwAIAAeAB8AHwAhACAAHgAiABsAGwAfAB4AGwAaAB8AHQAjACQAJAAlAB0AJQAcAB0AIwAdABsAGwAiACMAJwAmABgAGAAXACcAJAAjACIAIgAoACQAIgAeACgAHgApACgAHgAqACkAHgAgACoAGQAWAAwADAANABkADQAPABkAKwAhAB8AJwArAB8AJgAnAB8AJgAfABoAGgAcACYAJQAmABwAKQADAAEAAQAoACkAGAAmACUAJQARABgAJQAkABEAAAARACQAAAAkACgAKAABAAAAAAAFABEAEwARAAUAEwAFAAQABAAIABMAAgADACkAKQAqAAIAKgArAAIAKwAWAAIAFwAWACsAKwAnABcAFgAZAAIAGQAHAAIABgAHABkAGQAJAAYAKwAqACAAIAAhACsA"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 44,
"vertex_data": PackedByteArray("vHQTPjvGOjK8dBM+vHQTPjvGOrK8dBO+vHQTvjvGOjK8dBM+vHQTvjvGOrK8dBO+Yr68PZuPa0CHvry9Yr68PZuPa0A9vrw9Yr68vZuPa0CHvry9Yr68vZuPa0A9vrw933rDPWGdXEACe8O9vPrCvaa5XUDf+sK9jXYSvwc0MkBMnky+pPFlv59AS0BOnky+jXYSvwc0MkCKEoO9pPFlv59AS0COEoO9Qr8Bv2gfYEBQnky+Qr8Bv2gfYECREoO9QKA0P+nCKkCJEoO901/YPW1BLkCKEoO9i1dFP0muWECQEoO933rDPWGdXECREoO9i1dFP0muWEBPnky+QKA0P+nCKkBMnky+BNLXvRd8L0CKEoO9BNLXvRd8L0Ag0te901/YPW1BLkDvX9i9vPrCvaa5XUCREoO9OgEBPzYbHEBKnky+55NQPz9GAkBInky+OgEBPzYbHECHEoO955NQPz9GAkCDEoO9g3Rcv8Pp8T9Inky+UrNEv4SuJkBLnky+g3Rcv8Pp8T+BEoO9UrNEv4SuJkCIEoO9EoDSPifD3D9Hnky+EoDSPifD3D9/EoO9qhH0Paqd4T+AEoO9CxPfPbxjH0CHEoO9CxPfPbxjH0AkE9+9gULevXIyIUCbQt69qhH0Paqd4T+8EfS9fy3zvSuS5T+RLfO9fy3zvSuS5T+AEoO9gULevXIyIUCIEoO9")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_43w1m"]
resource_name = "SignpostDouble_SignpostDouble"
_surfaces = [{
"aabb": AABB(-0.898218, -1.08717e-08, -0.199823, 1.71298, 3.68064, 0.343833),
"attribute_data": PackedByteArray("1Wo1P1YqlT7VajU/CEeBP9VqNb8IR4E/1Wo1P2q12j/VajW/CEeBP9VqNT8IR4E/1Wo1PwhHgT/VajW/ViqVPtVqNb8IR4E/1Wo1vwhHgT/VajW/arXaP9VqNT8IR4E/1DbovnPXiMHUNui+tQ26P9Q26D5z14jB1DboPnPXiMHUNui+luQLP9Q26L5z14jB1DbovnPXiMHUNug+c9eIwdQ26D61Dbo/1DboPnPXiMHUNui+c9eIwdQ26D6W5As/gYDwvvRKf8GBgPA+cMCOvYGA8D70Sn/B4OLvvmFUgMHg4u8+YVSAweDi7z7wg4G/p757v4RHNcGnvnu//u9CwBoyNED/PkvBp757v49kXcGnvnu/YHazwKVzjUCCEGrBhEKhvoRHNcGEQqG+/u9CwBoyNMD/PkvBhEKhvo9kXcGEQqG+YHazwKVzjcCCEGrBp757vzTYAcE8oR9A6N6Bwae+ez/+70LAhEKhvjTYAcE8oR/A6N6BwYRCoT7+70LAhEKhvu4lUUAROl5AGhdCwYRCoT7EQkbBhEKhvghVmz6xGgU/qGNGwYRCoT46QEbBhEKhPjpARsHzynJA6pV6wYRCoT7uJVFAhEKhPmD9fsGBgPA+9Gx/wYRCoT5wwI69hEKhPvRKf8HzynLA6pV6wae+ez/uJVFAp757P2D9fsGnvnu/7iVRQBE6XsAaF0LBp757P8RCRsGEQqG+YcNHwYRCob7sTD2/c8MEv8XmR8FzwwS/YcNHwXPDBL/sTD2/c8MEP2HDR8GxGgW/CFWbPrEaBb86QEbBsRoFPzpARsGEQqG+YVSAweDi775bZYDBhEKhPvCDgb9vtx7AUg8wwae+ez++5fQ/p757PwHq38DmToDAUEcQwae+ez8unwTBp757P/mvj8Bvtx5AUg8wwYRCoT6+5fQ/hEKhPgHq38DmToBAUEcQwYRCoT4unwTBhEKhPvmvj8Cnvnu/Eq/1wKe+e7+H0YzAWJ2HQI7QBMGnvnu/G5IzwecAckAwEj3Bp757P4fRjMCEQqG+Eq/1wIRCob6H0YzAWJ2HwI7QBMGEQqG+G5IzwecAcsAwEj3BhEKhPofRjMCnvnu/vuX0P6Z9AcBsm+/Ap757P1MEucCEQqG+vuX0P6Z9AUBsm+/AhEKhPlMEucCEQqG+MMTtPhEkFj8elPXAhEKhPmhJ9cCEQqE+aEn1wNU5CT9wGTTBhEKhPsBlQL2EQqE+jvUzwYRCoT6O9TPB1TkJv471M8HVOQk/wGVAvdU5CT+O9TPBjLkIv+YuNsGMuQg/5i42wYy5CD9kI5C/ESQWvzDE7T4RJBa/aEn1wBEkFj9oSfXAs5cVvz4n+sCzlxW/IF43v7OXFT8+J/rAhEKhvj4n+sCEQqG+IF43v7OXFb/UcfrAhEKhvuYuNsGMuQi/vVI2wYRCoT5kI5C/"),
"format": 34359742487,
"index_count": 252,
"index_data": PackedByteArray("BwAAAAMAAwAKAAcAFAANABAAEAAXABQAHAAYAAwADAATABwAJAAeACEAIQAnACQAIgAqAC0ALQAoACIADwABAAgACAAWAA8ANwAxADQANAA6ADcAOQA/AEIAQgAyADkAJQBEAEcAHwAlAEcAHwBHAEkAQAAfAEkAQABJADMAMwAwAEAAIABBAD0APQAjACAAPQArACMALAA+ADgAOAAZACwAOwAZADgAGQAdACwAHQBOACwATgAvACwAVwBRAFQAVABaAFcAYQBbAF4AXgBkAGEAXQBoAFIAUgBfAF0AUgBPAF8AWABrAG4AbgBxAFgAcQBVAFgAbABZAFMAUwBpAGwAeQB1AEoASgBIAHkAbQBqAGcAZwB7AG0AZwBcAHsAXAB/AHsAXACCAH8AXABiAIIATQBFACYAJgApAE0AKQAuAE0AhgBmAGAAegCGAGAAdgB6AGAAdgBgAFAAUABWAHYAcgB2AFYAgAALAAQABAB8AIAASwB3AHQAdAA2AEsAcwBvADUAAgA2AHAAAgBwAH0AfQAFAAIAAgARADYAPAA2ABEAPAARAA4ADgAaADwABgAJAH4AfgCBAAYAgQCEAAYAhABDAAYARgBDAIQAhAB4AEYAQwBMAAYATAAVAAYAEgAVAEwATAAbABIAhQCDAGMAYwBlAIUA"),
"material": SubResource("StandardMaterial3D_lj8wk"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 135,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_tnp6p")

[sub_resource type="BoxShape3D" id="BoxShape3D_64ksg"]
size = Vector3(1.70251, 1.78321, 0.367398)

[sub_resource type="BoxShape3D" id="BoxShape3D_eu7jb"]
size = Vector3(0.373156, 3.5633, 0.337395)

[node name="SignpostDouble" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_43w1m")
skeleton = NodePath("")
surface_material_override/0 = SubResource("StandardMaterial3D_lj8wk")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0406952, 2.62475, -0.0325559)
shape = SubResource("BoxShape3D_64ksg")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00495535, 1.89335, 0.00310308)
shape = SubResource("BoxShape3D_eu7jb")
