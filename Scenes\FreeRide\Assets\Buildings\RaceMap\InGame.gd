extends StateMachineState
class_name VehicleRaceIngameState

@onready var vehicleRace: Node3D = $"../.."
@onready var ResultsState: VehicleRaceResultState = $"../Results"
@onready var vehicleRaceStateMachine: VehicleStateMachine = $".."
@onready var hud: Control = $HUD
@onready var checkpoint_label: Label = $HUD/CheckPointLabel


func _ready() -> void:
	hud.visible = false
	hud.process_mode = Node.PROCESS_MODE_DISABLED


func on_enter() -> void:
	if Constants.is_server:
		vehicleRaceStateMachine.players_checkpoint = [0, 0, 0, 0]
		vehicleRaceStateMachine.finished_count = 0
		vehicleRaceStateMachine.ranks = []
		vehicleRaceStateMachine.time_counter = 0
		vehicleRaceStateMachine.start_time = Time.get_ticks_msec()
		vehicleRaceStateMachine.ingame_start_players = vehicleRaceStateMachine.player_count()
		var data = {
			"type": "VehicleRace",
			"players": [],
			"price": Selector.vehicle_race_price * 1
		}
		for p in vehicleRaceStateMachine.players:
			if p != null:
				data["players"].append(Constants.server.players_data[p]["backend_id"])
		data["godot_id"] = "RaceMap"
		BackendManager.send_start_minigame_request(data)
	
	if Constants.is_client():
		if vehicleRaceStateMachine.client_im_in:
			VehicleManager.my_vehicle().should_stop = false
		
		hud.visible = true
		hud.process_mode = Node.PROCESS_MODE_ALWAYS


func on_process(delta: float) -> void:
	if Constants.is_client():
		checkpoint_label.text = str(vehicleRaceStateMachine.client_checkpoint) + " / " + str(vehicleRaceStateMachine.MAX_CHECKPOINTS)
	else:
		vehicleRaceStateMachine.time_counter += delta
		if vehicleRaceStateMachine.time_counter > vehicleRaceStateMachine.INGAME_TIME:
			vehicleRaceStateMachine.on_times_up()


func on_physics_process(_delta: float) -> void:
	pass


func on_input(_event: InputEvent) -> void:
	pass


func on_exit() -> void:
	if Constants.is_client():
		hud.visible = false
		hud.process_mode = Node.PROCESS_MODE_DISABLED


func encode(buffer:PackedByteArray):
	var _start_index = buffer.size()
	buffer.resize(buffer.size() + 0)
	return buffer


func decode(_start_index:int, _buffer:PackedByteArray):
	#turn_time_counter = buffer.decode_u8(start_index)
	pass
