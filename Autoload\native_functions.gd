extends Node

var instance = null

func _ready():
	if Engine.has_singleton("NativeMethods"):
		instance = Engine.get_singleton("NativeMethods")


func open_store_page():
	OS.shell_open(NativeMarket.store_page())


func open_rate():
	if not instance:
		return
	
	if NativeMarket.market == NativeMarket.MarketType.CafeBazaar:
		cafebazaar_rate()
	elif NativeMarket.market == NativeMarket.MarketType.Myket:
		instance.MyketRate("ir.sizakgames.animalrushmmo")
	else:
		OS.shell_open(NativeMarket.rate_page())


func open_support():
	OS.shell_open(NativeMarket.support_page())
	#OS.shell_open("https://www.goftino.com/c/F9kbnH")


func share(title, subject, body):
	if instance:
		instance.share(title, subject, body)


func cafebazaar_rate():
	if instance:
		instance.CafebazaarRate("ir.sizakgames.animalrushmmo")


func getDeviceName():
	if instance:
		return instance.getDeviceName()
	else:
		return "Desktop"


func toast(_str):
	if instance:
		instance.Toast(_str)


func getBuildNumber():
	if instance:
		return instance.getBuildVersion()
	if Constants.is_mobile():
		return -1
	return 1000


func secret_key():
	if instance:
		return instance.getSecretKey()
	
	return ""
