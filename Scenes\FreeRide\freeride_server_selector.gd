extends Control


@onready var scroll = $Scroll
@onready var v_box_container = $Scroll/VBoxContainer
@onready var loading = $Loading
@onready var error = $Error
@onready var list_http_request = $ListHTTPRequest
@onready var panel = $Panel
@onready var empty = $Empty
@onready var retry_button1 = $Error/RetryButton
@onready var retry_button2 = $Empty/RetryButton
@onready var race_mode: Panel = $RaceMode
@onready var password_parent: ExitableControl = $PasswordParent
@onready var line_edit: LineEdit = $PasswordParent/Panel/LineEdit
@onready var filter_scroll: ScrollContainer = $FilterScroll
@onready var exitable_control: ExitableControl = $ExitableControl
@onready var selected_filter_label: Label = $FilterButton/label
@onready var filter_container: VBoxContainer = $FilterScroll/FilterContainer


var server_row_scene = preload("res://Scenes/ui/server_row.tscn")
var just_tests = false
var selected_filter: Selector.ServerFilters = Selector.ServerFilters.All


func _ready():
	exit_password_panel()
	list_http_request.request_completed.connect(on_list_request_complete)
	send_list_request()
	Selector.need_to_sync = true
	filter_scroll.visible = false
	exitable_control.visible = false
	for filter in filter_container.get_children():
		filter.select.connect(on_filter_signal)
	
	#Constants.print_orphans("Server Selector")


func send_list_request():
	hide_all_ui()
	loading.visible = true
	var url = Constants.BACKEND_URL + "/game/list_freeride_servers/"
	var data = {
		"key": NativeFunctions.secret_key(),
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	list_http_request.cancel_request()
	list_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func on_list_request_complete(_result, response_code, _headers, body):
	hide_all_ui()
	if response_code == 200:
		scroll.visible = true
		scroll.scroll_vertical = 0
		race_mode.visible = true
		var json = JSON.parse_string(body.get_string_from_utf8())
		handle_servers(json["servers"])
	else:
		if response_code == 400: # NO Server available:
			empty.visible = true
		else:
			error.visible = true


func add_server_row(server_data):
	var scene = server_row_scene.instantiate()
	v_box_container.add_child(scene)
	scene.set_data(server_data)
	scene.on_join.connect(on_join_pressed)


func handle_servers(data):
	for child in v_box_container.get_children():
		child.queue_free()
	
	var full_list = []
	for server_data in data:
		if just_tests and Constants.is_desktop():
			if server_data["is_test"]:
				add_server_row(server_data)
			continue

		if server_data["players_joined"] >= server_data["max_players_start"]:
			full_list.append(server_data)
		else:
			add_server_row(server_data)
	
	for server_data in full_list:
		add_server_row(server_data)
	
	
	panel.visible = true
	await get_tree().process_frame
	filter_servers(selected_filter)
	await get_tree().process_frame
	
	

	var counter = 0
	var timer = 0
	for scene in v_box_container.get_children():
		if scene.visible == false:
			continue
		var final_pos = scene.position
		scene.position.x = get_viewport_rect().size.x
		var tween = get_tree().create_tween()
		tween.tween_property(scene, "position", final_pos, 1 + timer).set_trans(Tween.TRANS_QUINT).set_ease(Tween.EASE_IN_OUT)
		tween.play()
		timer += 0.3
		counter += 1
		if counter > 10:
			break
	
	await get_tree().process_frame
	await get_tree().process_frame
	panel.visible = false


func filter_servers(filter: Selector.ServerFilters):
	for row in v_box_container.get_children():
		var data = row.data
		match filter:
			Selector.ServerFilters.All:
				row.visible = true
			Selector.ServerFilters.Empty:
				row.visible = data["players_joined"] < data["max_players_start"]
			Selector.ServerFilters.GunAllowed:
				row.visible = data["gun_allow"]
			Selector.ServerFilters.NotGunAllowed:
				row.visible = !data["gun_allow"]


func hide_all_ui():
	panel.visible = false
	loading.visible = false
	error.visible = false
	scroll.visible = false
	empty.visible = false
	race_mode.visible = false


func _on_exit_pressed():
	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")


var last_server_data = null
func on_join_pressed(server_data):
	Constants.URL = server_data["ip"]
	Constants.PORT = server_data["port"]
	Selector.freeride_id = server_data["id"]
	Selector.freeride_server_name = server_data["name"]
	last_server_data = server_data
	if Selector.free_admin:
		if server_data["has_password"]:
			show_password_popup()
			return
		get_tree().change_scene_to_file("res://Scenes/FreeRide/free_ride.tscn")
		return
	if server_data["players_joined"] >= server_data["max_players_start"]:
		print("FULL")
	else:
		if server_data["has_password"]:
			show_password_popup()
			return
		get_tree().change_scene_to_file("res://Scenes/FreeRide/free_ride.tscn")


func _on_retry_button_pressed():
	send_list_request()


func _on_retry_button_button_down():
	retry_button1.scale = Vector2(0.9, 0.9)
	retry_button2.scale = Vector2(0.9, 0.9)


func _on_retry_button_button_up():
	retry_button1.scale = Vector2(1, 1)
	retry_button2.scale = Vector2(1, 1)


func _on_restart_button_pressed():
	send_list_request()


func _on_race_mode_join_button_pressed() -> void:
	get_tree().change_scene_to_file("res://Scenes/game_scene.tscn")


func exit_password_panel():
	password_parent.visible = false


func show_password_popup():
	password_parent.visible = true
	line_edit.text = ""


func _on_password_join_button_pressed() -> void:
	var text = line_edit.text
	if last_server_data["password"] != text:
		Constants.show_toast(tr("WRONG_PASS"))
		return

	get_tree().change_scene_to_file("res://Scenes/FreeRide/free_ride.tscn")


func _on_filter_button_pressed() -> void:
	filter_scroll.visible = true
	exitable_control.visible = true


func _on_exitable_control_touched() -> void:
	pass # Replace with function body.


func on_filter_signal(filter, text):
	selected_filter = filter
	filter_scroll.visible = false
	selected_filter_label.text = tr(text)
	exitable_control.visible = false
	filter_servers(filter)
