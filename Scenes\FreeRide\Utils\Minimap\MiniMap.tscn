[gd_scene load_steps=36 format=3 uid="uid://dm2q3pf8gnlfl"]

[ext_resource type="Script" path="res://Scenes/FreeRide/Utils/Minimap/MiniMap.gd" id="1_aqcwn"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="2_3rdtb"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="2_vjx8a"]
[ext_resource type="Texture2D" uid="uid://ce3vv7gk3d83y" path="res://Scenes/ui/assets/minimap.png" id="3_1tq0s"]
[ext_resource type="Texture2D" uid="uid://cdi8kfb3vhhfs" path="res://Scenes/ui/assets/button-back.png" id="3_j0erf"]
[ext_resource type="Theme" uid="uid://cavx3qitdfs8s" path="res://Scenes/ui/ui_theme.tres" id="5_3cprf"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="6_km7l2"]
[ext_resource type="Texture2D" uid="uid://bvup2u17dl8s7" path="res://Scenes/ui/assets/checked.png" id="8_op1hb"]
[ext_resource type="Texture2D" uid="uid://cem3qcjxa4nf0" path="res://Scenes/ui/assets/unchecked.png" id="9_8lpcm"]
[ext_resource type="StyleBox" uid="uid://3jmm6a0rtg6b" path="res://Scenes/ui/panel_background.tres" id="10_gqpwm"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Utils/Minimap/AdminPanel.gd" id="11_tfudc"]
[ext_resource type="Texture2D" uid="uid://dotyplnxxun7r" path="res://Scenes/ui/assets/tick.png" id="12_gd6pw"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Utils/Minimap/BazrasPanel.gd" id="13_8gymo"]
[ext_resource type="PackedScene" uid="uid://dfw3vd3t4jtva" path="res://Scenes/ui/coin_show.tscn" id="13_wie24"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Utils/Minimap/gps.gd" id="14_1q47h"]
[ext_resource type="StyleBox" uid="uid://b46g8n4qu2fl3" path="res://Scenes/ui/assets/tab_normal_gray.tres" id="14_hdgkn"]
[ext_resource type="Texture2D" uid="uid://d3yrew1umwm4l" path="res://Scenes/FreeRide/Assets/Buildings/ToolShop/Sources/gps_digital-grid-background-4061179355.jpg" id="14_uix4x"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Utils/Minimap/EliteBazrasPanel.gd" id="15_3rie2"]
[ext_resource type="Texture2D" uid="uid://dytrwp166eb0r" path="res://Scenes/ui/assets/search.png" id="15_vybhe"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="16_miw6l"]
[ext_resource type="Texture2D" uid="uid://bw3w61kw7wpki" path="res://Scenes/ui/assets/PNG/grey_crossWhite.png" id="16_w3tfa"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Utils/Minimap/ReportList.gd" id="18_5ulsc"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_esspd"]
texture = ExtResource("2_3rdtb")

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5ugtl"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_7fq7q"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1x25n"]
bg_color = Color(0.374139, 0.374139, 0.374139, 1)
corner_radius_top_left = 3
corner_radius_top_right = 3
corner_radius_bottom_right = 3
corner_radius_bottom_left = 3

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_00act"]
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_hyk7d"]
bg_color = Color(0.877616, 0.877616, 0.877616, 1)
border_width_left = 5
border_width_top = 5
border_width_right = 5
border_width_bottom = 5
border_color = Color(0.0901961, 0.0901961, 0.0901961, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_21of1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_pphee"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5rtsl"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_3pdxq"]
bg_color = Color(0, 1, 0.152941, 1)
border_width_left = 4
border_width_top = 4
border_width_right = 4
border_width_bottom = 4
border_color = Color(0.194349, 0.194349, 0.194349, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_llith"]
bg_color = Color(0.372549, 0.372549, 0.372549, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_sjkow"]
bg_color = Color(0.25098, 0.25098, 0.25098, 0.588235)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_avbyb"]
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="MiniMap" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("5_3cprf")
script = ExtResource("1_aqcwn")
MIN_X = -500.0
MAX_X = 480.0
MIN_Z = -580.0
MAX_Z = 310.0

[node name="Panel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_esspd")

[node name="Map" type="Control" parent="."]
custom_minimum_size = Vector2(100, 100)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 170.0
offset_top = 105.0
offset_right = -170.0
offset_bottom = -86.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="Panel" type="TextureRect" parent="Map"]
custom_minimum_size = Vector2(860, 529)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -430.0
offset_top = -264.5
offset_right = 430.0
offset_bottom = 264.5
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_1tq0s")

[node name="Remotes" type="Control" parent="Map"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="vehicles" type="Control" parent="Map"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="ExitButton" parent="." instance=ExtResource("2_vjx8a")]
custom_minimum_size = Vector2(50, 50)
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 56.0
offset_top = 20.0
offset_right = 138.0
offset_bottom = 93.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(53, 49)

[node name="TextureRect" type="TextureRect" parent="ExitButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 7.0
offset_right = 7.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("3_j0erf")
expand_mode = 1
stretch_mode = 4

[node name="ExitButton2" parent="." instance=ExtResource("2_vjx8a")]
custom_minimum_size = Vector2(50, 50)
layout_mode = 1
anchors_preset = 13
anchor_left = 0.5
anchor_right = 0.5
offset_left = 450.0
offset_right = 1759.0
pivot_offset = Vector2(654.5, 360)

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 170.0
offset_top = -85.0
offset_right = -170.0
grow_horizontal = 2
grow_vertical = 0
theme = ExtResource("5_3cprf")
theme_override_constants/separation = 20
alignment = 1

[node name="ShowNames" type="CheckBox" parent="HBoxContainer"]
layout_mode = 2
theme_override_constants/h_separation = 5
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = SubResource("StyleBoxEmpty_5ugtl")
theme_override_constants/check_v_offset = -1
theme_override_icons/checked = ExtResource("8_op1hb")
theme_override_icons/unchecked = ExtResource("9_8lpcm")
button_pressed = true
text = "MAP_SHOW_NAMES"

[node name="ShowRemotes" type="CheckBox" parent="HBoxContainer"]
layout_mode = 2
theme_override_constants/h_separation = 5
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = SubResource("StyleBoxEmpty_5ugtl")
theme_override_constants/check_v_offset = -1
theme_override_icons/checked = ExtResource("8_op1hb")
theme_override_icons/unchecked = ExtResource("9_8lpcm")
button_pressed = true
text = "MAP_SHOW_REMOTES"

[node name="ShowVehicles" type="CheckBox" parent="HBoxContainer"]
layout_mode = 2
theme_override_constants/h_separation = 5
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = SubResource("StyleBoxEmpty_5ugtl")
theme_override_constants/check_v_offset = -1
theme_override_icons/checked = ExtResource("8_op1hb")
theme_override_icons/unchecked = ExtResource("9_8lpcm")
button_pressed = true
text = "MAP_SHOW_VEHICLES"

[node name="Title" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -41.5
offset_top = 24.0
offset_right = 41.5
offset_bottom = 50.0
grow_horizontal = 2
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "MAP_TITLE"

[node name="ScrollContainer" type="ScrollContainer" parent="."]
layout_mode = 0
offset_left = 42.0
offset_top = 146.0
offset_right = 161.0
offset_bottom = 619.0
horizontal_scroll_mode = 0

[node name="VBoxContainer" type="VBoxContainer" parent="ScrollContainer"]
clip_contents = true
layout_direction = 3
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 0

[node name="UsersTitle" type="Label" parent="."]
layout_mode = 0
offset_left = 36.0
offset_top = 88.0
offset_right = 170.0
offset_bottom = 145.0
theme_override_fonts/font = ExtResource("6_km7l2")
text = "MAP_USERS"
horizontal_alignment = 1
vertical_alignment = 1
clip_text = true

[node name="Admin" type="Control" parent="."]
visible = false
z_index = 100
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("11_tfudc")

[node name="AdminPanel" type="Panel" parent="Admin"]
custom_minimum_size = Vector2(800, 400)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -293.0
offset_right = 400.0
offset_bottom = 286.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = ExtResource("10_gqpwm")

[node name="ScrollContainer" type="ScrollContainer" parent="Admin/AdminPanel"]
layout_mode = 1
offset_left = 12.0
offset_top = 56.0
offset_right = 291.0
offset_bottom = 542.0
horizontal_scroll_mode = 0
scroll_deadzone = 70

[node name="VBoxContainer" type="VBoxContainer" parent="Admin/AdminPanel/ScrollContainer"]
clip_contents = true
layout_direction = 3
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 10

[node name="Selected" type="Control" parent="Admin/AdminPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 276.0
offset_top = 55.0
offset_right = -17.0
offset_bottom = -22.0
grow_horizontal = 2
grow_vertical = 2

[node name="SelectedName" type="Label" parent="Admin/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -41.5
offset_top = 24.0
offset_right = 41.5
offset_bottom = 50.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "Name"
horizontal_alignment = 1

[node name="SelectedID" type="Label" parent="Admin/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -97.0
offset_top = 80.0
offset_right = 97.0
offset_bottom = 147.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "ID"
horizontal_alignment = 1

[node name="Character" type="Label" parent="Admin/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -97.0
offset_top = 136.0
offset_right = 97.0
offset_bottom = 203.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "Character"
horizontal_alignment = 1

[node name="VBoxContainer" type="HBoxContainer" parent="Admin/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -106.0
offset_bottom = -72.0
grow_horizontal = 2
grow_vertical = 0
theme_override_constants/separation = 30
alignment = 1

[node name="BanButton" type="Button" parent="Admin/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban"

[node name="UnBanButton" type="Button" parent="Admin/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "UnBan"

[node name="KickButton" type="Button" parent="Admin/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Kick"

[node name="Title" type="HBoxContainer" parent="Admin/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -53.0
offset_bottom = -19.0
grow_horizontal = 2
grow_vertical = 0
theme_override_constants/separation = 30
alignment = 1

[node name="ClearTitleButton" type="Button" parent="Admin/AdminPanel/Selected/Title"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Clear Title"

[node name="MenuBar" type="MenuBar" parent="Admin/AdminPanel/Selected/Title"]
layout_mode = 2

[node name="Job" type="PopupMenu" parent="Admin/AdminPanel/Selected/Title/MenuBar"]
min_size = Vector2i(80, 0)

[node name="SelectedJob" type="Label" parent="Admin/AdminPanel/Selected/Title"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 20
text = "Job"
horizontal_alignment = 1

[node name="Add Title" type="Button" parent="Admin/AdminPanel/Selected/Title"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Add Title"

[node name="Tick" type="TextureRect" parent="Admin/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -24.0
offset_top = -8.0
offset_right = 24.0
offset_bottom = 40.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("12_gd6pw")

[node name="Shahrdar" type="Button" parent="Admin/AdminPanel/Selected"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -108.0
offset_bottom = 37.0
grow_horizontal = 0
size_flags_horizontal = 4
text = "رییس جمهور"

[node name="AliveMe" type="Button" parent="Admin/AdminPanel/Selected"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -108.0
offset_top = 45.0
offset_bottom = 82.0
grow_horizontal = 0
size_flags_horizontal = 4
text = "زنده شو"

[node name="Title" type="Label" parent="Admin/AdminPanel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -97.0
offset_right = 97.0
offset_bottom = 51.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 30
text = "ADMIN_TITLE"
horizontal_alignment = 1

[node name="BanHTTPRequest" type="HTTPRequest" parent="Admin"]

[node name="TitleHTTPRequest" type="HTTPRequest" parent="Admin"]

[node name="UNBanHTTPRequest" type="HTTPRequest" parent="Admin"]

[node name="Bazras" type="Control" parent="."]
visible = false
z_index = 100
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("13_8gymo")

[node name="AdminPanel" type="Panel" parent="Bazras"]
custom_minimum_size = Vector2(800, 400)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -293.0
offset_right = 443.0
offset_bottom = 286.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = ExtResource("10_gqpwm")

[node name="ScrollContainer" type="ScrollContainer" parent="Bazras/AdminPanel"]
layout_mode = 1
offset_left = 12.0
offset_top = 56.0
offset_right = 291.0
offset_bottom = 542.0
horizontal_scroll_mode = 0
scroll_deadzone = 70

[node name="VBoxContainer" type="VBoxContainer" parent="Bazras/AdminPanel/ScrollContainer"]
clip_contents = true
layout_direction = 3
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 10

[node name="Selected" type="Control" parent="Bazras/AdminPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 276.0
offset_top = 55.0
offset_right = -17.0
offset_bottom = -22.0
grow_horizontal = 2
grow_vertical = 2

[node name="SelectedName" type="Label" parent="Bazras/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -41.5
offset_top = 24.0
offset_right = 41.5
offset_bottom = 50.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "Name"
horizontal_alignment = 1

[node name="SelectedID" type="Label" parent="Bazras/AdminPanel/Selected"]
visible = false
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -97.0
offset_top = 80.0
offset_right = 97.0
offset_bottom = 147.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "ID"
horizontal_alignment = 1

[node name="Character" type="Label" parent="Bazras/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -97.0
offset_top = 80.0
offset_right = 97.0
offset_bottom = 147.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "Character"
horizontal_alignment = 1

[node name="VBoxContainer" type="GridContainer" parent="Bazras/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 26.0
offset_top = -106.0
offset_right = 193.0
offset_bottom = 135.0
grow_horizontal = 2
grow_vertical = 0
theme_override_constants/h_separation = 5
theme_override_constants/v_separation = 10
columns = 5

[node name="Ban5mButton" type="Button" parent="Bazras/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 5m"

[node name="Ban30mButton" type="Button" parent="Bazras/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 30m"

[node name="Ban1hButton" type="Button" parent="Bazras/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 1h"

[node name="Ban4hButton" type="Button" parent="Bazras/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 4h"

[node name="Ban1dButton" type="Button" parent="Bazras/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 1d"

[node name="UnBanButton" type="Button" parent="Bazras/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "رفع مسدودی"

[node name="ClearTitleButton" type="Button" parent="Bazras/AdminPanel/Selected/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "اخراج شغل"

[node name="Tick" type="TextureRect" parent="Bazras/AdminPanel/Selected"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -24.0
offset_top = -96.0
offset_right = 24.0
offset_bottom = -48.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("12_gd6pw")

[node name="UndercoverMode" type="CheckBox" parent="Bazras/AdminPanel/Selected"]
layout_mode = 0
offset_left = 288.0
offset_top = 464.0
offset_right = 554.0
offset_bottom = 512.0
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = SubResource("StyleBoxEmpty_7fq7q")
theme_override_icons/checked = ExtResource("8_op1hb")
theme_override_icons/unchecked = ExtResource("9_8lpcm")
button_pressed = true
text = "مخفی شدن"
alignment = 1
icon_alignment = 2
expand_icon = true

[node name="Panel" type="Panel" parent="Bazras/AdminPanel/Selected/UndercoverMode"]
show_behind_parent = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = ExtResource("14_hdgkn")

[node name="Title" type="Label" parent="Bazras/AdminPanel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -97.0
offset_right = 97.0
offset_bottom = 51.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 30
text = "بازرس"
horizontal_alignment = 1

[node name="EliteButton" type="Button" parent="Bazras/AdminPanel"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -116.0
offset_top = 6.0
offset_right = -16.0
offset_bottom = 37.0
grow_horizontal = 0
size_flags_horizontal = 4
theme_override_styles/normal = SubResource("StyleBoxFlat_1x25n")
text = "ویژه"

[node name="ElitePanel" type="Panel" parent="Bazras/AdminPanel"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 8.0
offset_top = 45.0
offset_right = -7.0
offset_bottom = -8.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_00act")
script = ExtResource("15_3rie2")

[node name="HBoxContainer" type="HBoxContainer" parent="Bazras/AdminPanel/ElitePanel"]
custom_minimum_size = Vector2(0, 100)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -270.0
offset_top = -5.0
offset_right = 270.0
offset_bottom = 95.0
grow_horizontal = 2
mouse_filter = 2
theme_override_constants/separation = 10
alignment = 1
metadata/_edit_lock_ = true

[node name="PasteButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/HBoxContainer"]
custom_minimum_size = Vector2(100, 50)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
text = "الصاق paste"

[node name="TextBGPanel" type="Panel" parent="Bazras/AdminPanel/ElitePanel/HBoxContainer"]
custom_minimum_size = Vector2(300, 50)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
mouse_filter = 1
theme_override_styles/panel = SubResource("StyleBoxFlat_hyk7d")

[node name="SearchTextEdit" type="LineEdit" parent="Bazras/AdminPanel/ElitePanel/HBoxContainer/TextBGPanel"]
layout_direction = 3
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 1
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_constants/outline_size = 4
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = SubResource("StyleBoxEmpty_21of1")
theme_override_styles/read_only = SubResource("StyleBoxEmpty_pphee")
theme_override_styles/normal = SubResource("StyleBoxEmpty_5rtsl")
placeholder_text = "جستجو با شناسه"
alignment = 1

[node name="SearchButton" parent="Bazras/AdminPanel/ElitePanel/HBoxContainer" instance=ExtResource("2_vjx8a")]
custom_minimum_size = Vector2(100, 50)
layout_mode = 2
size_flags_vertical = 4
metadata/_edit_lock_ = true

[node name="BG" type="Panel" parent="Bazras/AdminPanel/ElitePanel/HBoxContainer/SearchButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_3pdxq")
metadata/_edit_lock_ = true

[node name="TextureRect" type="TextureRect" parent="Bazras/AdminPanel/ElitePanel/HBoxContainer/SearchButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 22.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("15_vybhe")
expand_mode = 1
stretch_mode = 4
flip_h = true

[node name="Selected" type="Control" parent="Bazras/AdminPanel/ElitePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 12.0
offset_top = 55.0
offset_right = -17.0
offset_bottom = -22.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="SelectedName" type="Label" parent="Bazras/AdminPanel/ElitePanel/Selected"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -48.5
offset_top = 24.0
offset_right = 48.5
offset_bottom = 88.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "Name"
horizontal_alignment = 1

[node name="DataContainer" type="VBoxContainer" parent="Bazras/AdminPanel/ElitePanel/Selected"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 544.0
offset_top = 58.0
offset_bottom = 40.0
grow_horizontal = 2
grow_vertical = 2

[node name="ID" type="Label" parent="Bazras/AdminPanel/ElitePanel/Selected/DataContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "ID"
horizontal_alignment = 1

[node name="username" type="Label" parent="Bazras/AdminPanel/ElitePanel/Selected/DataContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "username"
horizontal_alignment = 1

[node name="Title" type="Label" parent="Bazras/AdminPanel/ElitePanel/Selected/DataContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "Title"
horizontal_alignment = 1

[node name="Ban Time" type="Label" parent="Bazras/AdminPanel/ElitePanel/Selected/DataContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 40
text = "Ban Time"
horizontal_alignment = 1

[node name="ButtonContainer" type="GridContainer" parent="Bazras/AdminPanel/ElitePanel/Selected"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 26.0
offset_top = -82.0
offset_right = 11.0
offset_bottom = 16.0
grow_horizontal = 2
grow_vertical = 0
theme_override_constants/h_separation = 5
theme_override_constants/v_separation = 10
columns = 5

[node name="Ban5mButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 5m"

[node name="Ban30mButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 30m"

[node name="Ban1hButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 1h"

[node name="Ban4hButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 4h"

[node name="Ban1dButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "Ban 1d"

[node name="UnBanButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "رفع مسدودی"

[node name="ClearTitleButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "اخراج شغل"

[node name="PoliceReport" type="Button" parent="Bazras/AdminPanel/ElitePanel/Selected"]
custom_minimum_size = Vector2(100, 0)
offset_left = 24.0
offset_top = 72.0
offset_right = 124.0
offset_bottom = 103.0
size_flags_horizontal = 4
text = "بررسی سابقه"

[node name="ExitEliteButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/Selected"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 1
offset_left = 708.0
offset_top = -93.0
offset_right = 808.0
offset_bottom = -62.0
size_flags_horizontal = 4
theme_override_styles/normal = SubResource("StyleBoxFlat_llith")
text = "بازگشت"

[node name="Loading" type="Panel" parent="Bazras/AdminPanel/ElitePanel"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_sjkow")

[node name="LoadingElement" parent="Bazras/AdminPanel/ElitePanel/Loading" instance=ExtResource("16_miw6l")]
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0

[node name="SearchHTTPRequest" type="HTTPRequest" parent="Bazras/AdminPanel/ElitePanel"]

[node name="AliveMe" type="Button" parent="Bazras/AdminPanel/ElitePanel"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -125.0
offset_top = -53.0
offset_right = -17.0
offset_bottom = -16.0
grow_horizontal = 0
grow_vertical = 0
size_flags_horizontal = 4
text = "زنده شو"

[node name="ReportList" type="Panel" parent="Bazras/AdminPanel/ElitePanel"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_avbyb")
script = ExtResource("18_5ulsc")

[node name="ListHTTPRequest" type="HTTPRequest" parent="Bazras/AdminPanel/ElitePanel/ReportList"]

[node name="Loading" type="Panel" parent="Bazras/AdminPanel/ElitePanel/ReportList"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_sjkow")

[node name="LoadingElement" parent="Bazras/AdminPanel/ElitePanel/ReportList/Loading" instance=ExtResource("16_miw6l")]
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0

[node name="ExitReportButton" type="Button" parent="Bazras/AdminPanel/ElitePanel/ReportList"]
custom_minimum_size = Vector2(100, 0)
offset_left = 718.0
offset_top = -38.0
offset_right = 818.0
offset_bottom = -7.0
size_flags_horizontal = 4
theme_override_styles/normal = SubResource("StyleBoxFlat_llith")
text = "بازگشت"

[node name="ScrollContainer" type="ScrollContainer" parent="Bazras/AdminPanel/ElitePanel/ReportList"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 55.0
grow_horizontal = 2
grow_vertical = 2
horizontal_scroll_mode = 0

[node name="VBoxContainer" type="VBoxContainer" parent="Bazras/AdminPanel/ElitePanel/ReportList/ScrollContainer"]
layout_mode = 2

[node name="NameLabel" type="Label" parent="Bazras/AdminPanel/ElitePanel/ReportList"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 108.0
offset_right = -573.0
offset_bottom = -494.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 20
text = "مجرم"
vertical_alignment = 1
text_overrun_behavior = 1

[node name="CrimeLabel" type="Label" parent="Bazras/AdminPanel/ElitePanel/ReportList"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -77.0
offset_top = -256.0
offset_right = 77.0
offset_bottom = -224.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 20
text = "جرم"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BanTime" type="Label" parent="Bazras/AdminPanel/ElitePanel/ReportList"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -290.0
offset_top = -256.0
offset_right = -211.0
offset_bottom = -224.0
grow_horizontal = 0
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 20
text = "مسدودی"
vertical_alignment = 1

[node name="JailTime" type="Label" parent="Bazras/AdminPanel/ElitePanel/ReportList"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -194.0
offset_top = -256.0
offset_right = -100.0
offset_bottom = -224.0
grow_horizontal = 0
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 20
text = "زندان"
vertical_alignment = 1

[node name="Details" type="Panel" parent="Bazras/AdminPanel/ElitePanel/ReportList"]
visible = false
custom_minimum_size = Vector2(600, 600)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -347.0
offset_right = 300.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = ExtResource("10_gqpwm")

[node name="Title" type="Label" parent="Bazras/AdminPanel/ElitePanel/ReportList/Details"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -31.5
offset_right = 31.5
offset_bottom = 48.0
grow_horizontal = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 30
text = "متن گزارش"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Text" type="Label" parent="Bazras/AdminPanel/ElitePanel/ReportList/Details"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_top = 48.0
offset_right = -15.0
offset_bottom = -16.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 30
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 1

[node name="DetailsCancelButton" parent="Bazras/AdminPanel/ElitePanel/ReportList/Details" instance=ExtResource("2_vjx8a")]
custom_minimum_size = Vector2(150, 50)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -165.0
offset_right = -15.0
offset_bottom = 50.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(75, 25)

[node name="TextureRect2" type="TextureRect" parent="Bazras/AdminPanel/ElitePanel/ReportList/Details/DetailsCancelButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -12.5
offset_top = -13.0
offset_right = 12.5
offset_bottom = 13.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("16_w3tfa")
expand_mode = 1
stretch_mode = 5

[node name="BanHTTPRequest" type="HTTPRequest" parent="Bazras"]

[node name="TitleHTTPRequest" type="HTTPRequest" parent="Bazras"]

[node name="UNBanHTTPRequest" type="HTTPRequest" parent="Bazras"]

[node name="CoinShow" parent="." instance=ExtResource("13_wie24")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -182.0
offset_top = 32.0
offset_right = -32.0
offset_bottom = 82.0
grow_horizontal = 0
grow_vertical = 1
link_to_shop = false

[node name="GPS" type="Control" parent="."]
visible = false
anchors_preset = 0
offset_left = 199.0
offset_top = 24.0
offset_right = 391.0
offset_bottom = 103.0
script = ExtResource("14_1q47h")

[node name="TextureRect" type="TextureRect" parent="GPS"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_top = -34.5
offset_right = 66.0
offset_bottom = 34.5
grow_vertical = 2
texture = ExtResource("14_uix4x")
expand_mode = 1
stretch_mode = 4

[node name="GPSButton" type="Button" parent="GPS/TextureRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 0
flat = true

[node name="GPSTime" type="Label" parent="GPS"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_top = -28.5
offset_right = 66.0
offset_bottom = 28.5
grow_vertical = 2
theme_override_fonts/font = ExtResource("6_km7l2")
text = "00:00:00"
horizontal_alignment = 1
vertical_alignment = 1
clip_text = true

[node name="GPSPanel" type="Panel" parent="GPS"]
z_index = 1
custom_minimum_size = Vector2(400, 400)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -240.0
offset_top = 38.5
offset_right = 161.0
offset_bottom = 584.5
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = ExtResource("10_gqpwm")

[node name="ScrollContainer" type="ScrollContainer" parent="GPS/GPSPanel"]
layout_mode = 1
offset_left = 12.0
offset_top = 56.0
offset_right = 378.0
offset_bottom = 542.0
horizontal_scroll_mode = 0
scroll_deadzone = 70

[node name="VBoxContainer" type="VBoxContainer" parent="GPS/GPSPanel/ScrollContainer"]
clip_contents = true
layout_direction = 3
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 10

[node name="Title" type="Label" parent="GPS/GPSPanel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -97.0
offset_right = 97.0
offset_bottom = 51.0
grow_horizontal = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("6_km7l2")
theme_override_font_sizes/font_size = 30
text = "GPS"
horizontal_alignment = 1

[node name="CancelButton" parent="GPS/GPSPanel" instance=ExtResource("2_vjx8a")]
custom_minimum_size = Vector2(150, 50)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -165.0
offset_right = -15.0
offset_bottom = 50.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(75, 25)

[node name="TextureRect2" type="TextureRect" parent="GPS/GPSPanel/CancelButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -12.5
offset_top = -13.0
offset_right = 12.5
offset_bottom = 13.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("16_w3tfa")
expand_mode = 1
stretch_mode = 5

[node name="AdminButtons" type="HBoxContainer" parent="."]
custom_minimum_size = Vector2(0, 80)
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 14.0
offset_top = -93.0
offset_right = 280.0
offset_bottom = -13.0
grow_vertical = 0
theme_override_constants/separation = 20

[node name="AdminButton" type="Button" parent="AdminButtons"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
focus_mode = 0
text = "ADMIN"

[node name="BazrasButton" type="Button" parent="AdminButtons"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
focus_mode = 0
text = "بازرس"

[connection signal="pressed" from="ExitButton" to="." method="exit"]
[connection signal="pressed" from="ExitButton2" to="." method="exit"]
[connection signal="toggled" from="HBoxContainer/ShowNames" to="." method="_on_show_names_toggled"]
[connection signal="toggled" from="HBoxContainer/ShowRemotes" to="." method="_on_show_remotes_toggled"]
[connection signal="toggled" from="HBoxContainer/ShowVehicles" to="." method="_on_show_vehicles_toggled"]
[connection signal="gui_input" from="Admin" to="." method="_on_admin_gui_input"]
[connection signal="pressed" from="Admin/AdminPanel/Selected/VBoxContainer/BanButton" to="Admin" method="_on_ban_button_pressed"]
[connection signal="pressed" from="Admin/AdminPanel/Selected/VBoxContainer/UnBanButton" to="Admin" method="_on_un_ban_button_pressed"]
[connection signal="pressed" from="Admin/AdminPanel/Selected/VBoxContainer/KickButton" to="Admin" method="_on_kick_button_pressed"]
[connection signal="pressed" from="Admin/AdminPanel/Selected/Title/ClearTitleButton" to="Admin" method="_on_clear_title_button_pressed"]
[connection signal="index_pressed" from="Admin/AdminPanel/Selected/Title/MenuBar/Job" to="Admin" method="_on_job_index_pressed"]
[connection signal="pressed" from="Admin/AdminPanel/Selected/Title/Add Title" to="Admin" method="_on_add_title_pressed"]
[connection signal="pressed" from="Admin/AdminPanel/Selected/Shahrdar" to="Admin" method="_on_shahrdar_pressed"]
[connection signal="pressed" from="Admin/AdminPanel/Selected/AliveMe" to="Admin" method="_on_alive_me_pressed"]
[connection signal="request_completed" from="Admin/TitleHTTPRequest" to="Admin" method="_on_title_http_request_request_completed"]
[connection signal="ban_request_completed" from="Bazras" to="Bazras/AdminPanel/ElitePanel" method="_on_bazras_ban_request_completed"]
[connection signal="gui_input" from="Bazras" to="." method="_on_bazras_gui_input"]
[connection signal="pressed" from="Bazras/AdminPanel/Selected/VBoxContainer/Ban5mButton" to="Bazras" method="_on_ban_5m_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/Selected/VBoxContainer/Ban30mButton" to="Bazras" method="_on_ban_30m_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/Selected/VBoxContainer/Ban1hButton" to="Bazras" method="_on_ban_1h_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/Selected/VBoxContainer/Ban4hButton" to="Bazras" method="_on_ban_4h_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/Selected/VBoxContainer/Ban1dButton" to="Bazras" method="_on_ban_1d_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/Selected/VBoxContainer/UnBanButton" to="Bazras" method="_on_un_ban_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/Selected/VBoxContainer/ClearTitleButton" to="Bazras" method="_on_clear_title_button_pressed"]
[connection signal="toggled" from="Bazras/AdminPanel/Selected/UndercoverMode" to="Bazras" method="_on_undercover_mode_toggled"]
[connection signal="pressed" from="Bazras/AdminPanel/EliteButton" to="Bazras" method="_on_elite_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/HBoxContainer/PasteButton" to="Bazras/AdminPanel/ElitePanel" method="_on_paste_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/HBoxContainer/SearchButton" to="Bazras/AdminPanel/ElitePanel" method="_on_search_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer/Ban5mButton" to="Bazras/AdminPanel/ElitePanel" method="_on_ban_5m_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer/Ban30mButton" to="Bazras/AdminPanel/ElitePanel" method="_on_ban_30m_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer/Ban1hButton" to="Bazras/AdminPanel/ElitePanel" method="_on_ban_1h_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer/Ban4hButton" to="Bazras/AdminPanel/ElitePanel" method="_on_ban_4h_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer/Ban1dButton" to="Bazras/AdminPanel/ElitePanel" method="_on_ban_1d_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer/UnBanButton" to="Bazras/AdminPanel/ElitePanel" method="_on_un_ban_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/Selected/ButtonContainer/ClearTitleButton" to="Bazras/AdminPanel/ElitePanel" method="_on_clear_title_button_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/Selected/PoliceReport" to="Bazras/AdminPanel/ElitePanel" method="_on_police_report_pressed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/Selected/ExitEliteButton" to="Bazras/AdminPanel/ElitePanel" method="_on_exit_elite_button_pressed"]
[connection signal="request_completed" from="Bazras/AdminPanel/ElitePanel/SearchHTTPRequest" to="Bazras/AdminPanel/ElitePanel" method="_on_search_http_request_request_completed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/AliveMe" to="Bazras/AdminPanel/ElitePanel" method="_on_alive_me_pressed"]
[connection signal="request_completed" from="Bazras/AdminPanel/ElitePanel/ReportList/ListHTTPRequest" to="Bazras/AdminPanel/ElitePanel/ReportList" method="_on_list_http_request_request_completed"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/ReportList/ExitReportButton" to="Bazras/AdminPanel/ElitePanel/ReportList" method="exit"]
[connection signal="pressed" from="Bazras/AdminPanel/ElitePanel/ReportList/Details/DetailsCancelButton" to="Bazras/AdminPanel/ElitePanel/ReportList" method="_on_details_cancel_button_pressed"]
[connection signal="request_completed" from="Bazras/TitleHTTPRequest" to="Bazras" method="_on_title_http_request_request_completed"]
[connection signal="pressed" from="GPS/TextureRect/GPSButton" to="." method="_on_gps_button_pressed"]
[connection signal="pressed" from="GPS/GPSPanel/CancelButton" to="GPS" method="exit"]
[connection signal="pressed" from="AdminButtons/AdminButton" to="." method="_on_admin_button_pressed"]
[connection signal="pressed" from="AdminButtons/BazrasButton" to="." method="_on_bazras_button_pressed"]
