[gd_scene load_steps=17 format=3 uid="uid://d18spthncul43"]

[ext_resource type="Script" path="res://Scenes/FreeRide/Assets/TestFreeRide/TestFreeRideScene.gd" id="1_ykfp5"]
[ext_resource type="Texture2D" uid="uid://bj4c4guqfb5o4" path="res://HDRI/FS002_Day.png" id="2_qcusr"]
[ext_resource type="Texture2D" uid="uid://cqu41uuyn3afg" path="res://Scenes/FreeRide/Assets/TestFreeRide/texture_10.png" id="3_if4sm"]
[ext_resource type="PackedScene" uid="uid://0j68of81mi83" path="res://Scenes/FreeRide/Utils/VisibleStand.tscn" id="4_51v25"]
[ext_resource type="PackedScene" uid="uid://brgk5yn15emnp" path="res://assets/sfx/MusicStand.tscn" id="4_exh1y"]
[ext_resource type="PackedScene" uid="uid://de3qbtygqqs20" path="res://Scenes/Vehicle/Garage/VehicleGarage.tscn" id="8_03rmo"]
[ext_resource type="PackedScene" uid="uid://c7siquje4o0iv" path="res://Scenes/FreeRide/Assets/Buildings/Police/PoliceOffice.tscn" id="8_xwmwd"]
[ext_resource type="PackedScene" uid="uid://cxaypidrqv56e" path="res://Scenes/FreeRide/Assets/Buildings/Bank/ATM.tscn" id="9_4c5ps"]
[ext_resource type="PackedScene" uid="uid://dr88v5o4dp8re" path="res://Scenes/FreeRide/Assets/Buildings/RaceMap/VehicleRace.tscn" id="10_6khck"]
[ext_resource type="PackedScene" uid="uid://b4w2xoua116m3" path="res://Scenes/FreeRide/Assets/Actionables/Shops/CharacterSelectShop.tscn" id="10_hb1u6"]

[sub_resource type="PanoramaSkyMaterial" id="PanoramaSkyMaterial_qa2ri"]
panorama = ExtResource("2_qcusr")

[sub_resource type="Sky" id="Sky_4rcys"]
sky_material = SubResource("PanoramaSkyMaterial_qa2ri")

[sub_resource type="Environment" id="Environment_hgrox"]
background_mode = 2
background_energy_multiplier = 1.1
sky = SubResource("Sky_4rcys")
ambient_light_color = Color(1, 1, 1, 1)
ambient_light_sky_contribution = 0.65
glow_enabled = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_fevd7"]
albedo_color = Color(0.909804, 0.52549, 0.360784, 1)
albedo_texture = ExtResource("3_if4sm")
uv1_triplanar = true

[sub_resource type="BoxMesh" id="BoxMesh_7a5mj"]
size = Vector3(500, 0.5, 500)

[sub_resource type="BoxShape3D" id="BoxShape3D_axbvv"]
size = Vector3(497.638, 0.464, 1000)

[node name="TestFreeRideScene" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6.46467, 0, 0)
script = ExtResource("1_ykfp5")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_hgrox")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.633381, 0.562253, -0.531695, 0, 0.687087, 0.726575, 0.77384, -0.460199, 0.435188, 193.793, 30.2108, 4.01179)

[node name="MusicStand2" parent="." instance=ExtResource("4_exh1y")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 13.0356)

[node name="VisibleStand" parent="." instance=ExtResource("4_51v25")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -22.816)

[node name="Ground" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -43.567, -0.25707, 3.427)
material_override = SubResource("StandardMaterial3D_fevd7")
mesh = SubResource("BoxMesh_7a5mj")

[node name="StaticBody3D" type="StaticBody3D" parent="Ground"]
collision_layer = 2
collision_mask = 2097153

[node name="CollisionShape3D" type="CollisionShape3D" parent="Ground/StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.471, -0.00114441, 0)
shape = SubResource("BoxShape3D_axbvv")

[node name="PoliceOffice" parent="." instance=ExtResource("8_xwmwd")]
transform = Transform3D(-0.999487, 0, 0.0320212, 0, 1, 0, -0.0320212, 0, -0.999487, 39.5266, -0.111089, 27.3805)

[node name="CharacterSelect" parent="." instance=ExtResource("10_hb1u6")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20.2541, -0.0318636, -15.3795)
action_image = null

[node name="VehicleGarage" parent="." instance=ExtResource("8_03rmo")]
transform = Transform3D(-0.999881, 0, -0.0154281, 0, 1, 0, 0.0154281, 0, -0.999881, -1.38254, 0, -8.69204)

[node name="ATM" parent="." instance=ExtResource("9_4c5ps")]
transform = Transform3D(-0.9606, 0, -0.277935, 0, 1, 0, 0.277935, 0, -0.9606, 22.8902, 0, 9.14893)

[node name="RaceMap" parent="." instance=ExtResource("10_6khck")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 78.2993, 0, -105.254)
