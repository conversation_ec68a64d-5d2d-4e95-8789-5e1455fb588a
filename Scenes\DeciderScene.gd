extends Control


func _ready():
	Constants.handle_args()
	DataSaver.load_all()
	Selector.load_resources()
	
	if Constants.is_headless:#Headless Client
		ProjectSettings.set_setting("application/run/flush_stdout_on_print", true)
		ProjectSettings.save()
		get_tree().change_scene_to_file("res://Scenes/FreeRide/free_ride.tscn")
		return
	
	if Constants.is_client():
		on_client_init()
		return
	
	if Constants.is_server:
		if Constants.server_sync_v2:
			print("Server Sync V2")
		else:
			print("Normal Original Server Sync.")
		BackendManager.register_complete.connect(on_register_complete)
		on_server_init()
		return


func on_client_init():
	LocalNotification.init()
	NakamaManager.init()
	SoundManager.update_music_volume()
	SoundManager.update_sound_volume()
	if DataSaver.get_item("lang_select", "false") == false:
		call_deferred("start_lang_scene")
	else:
		TranslationServer.set_locale(DataSaver.get_item("locale", "en"))
		call_deferred("start_splash_scene")


func start_splash_scene():
	get_tree().change_scene_to_file("res://Scenes/splash_scene.tscn")


func start_lang_scene():
	get_tree().change_scene_to_file("res://Scenes/lang_scene.tscn")


func start_game_scene():
	get_tree().change_scene_to_file("res://Scenes/game_scene.tscn")


func on_server_init():
	if StageManager.is_finished:
		BackendManager.send_register_request()
	else:
		print_debug("change to game scene")
		call_deferred("start_game_scene")


func on_register_complete():
	if Constants.game_mode == Constants.GameMode.Race:
		get_tree().change_scene_to_file("res://Scenes/game_scene.tscn")
	else:
		get_tree().change_scene_to_file("res://Scenes/FreeRide/free_ride.tscn")
