extends Camera3D

## The distance, in meters, that the camera will be from the
## [Node3D] in [member follow_this].
@export var follow_distance = 5.0
## The height, in meters, that the camera will be from the
## [Node3D] in [member follow_this].
@export var follow_height = 2.0
## The speed, in meters per second, that the camera will
## move to reach the [Node3D] in [member follow_this].
## The [Node3D] that the camera will follow.
@export var follow_this : Node3D


func _physics_process(_delta : float):
	if not current:
		return
	if not is_instance_valid(follow_this):
		return

	var delta_v := global_transform.origin - follow_this.global_transform.origin
	delta_v.y = 0.0
	if (delta_v.length() > follow_distance):
		delta_v = delta_v.normalized() * follow_distance
		delta_v.y = follow_height
		global_position = follow_this.global_transform.origin + delta_v
	
	if follow_this.has_node("CameraLook"):
		look_at(follow_this.camera_look.global_position, Vector3.UP)
	else:
		look_at(follow_this.global_transform.origin, Vector3.UP)


func reset_camera():
	if not is_instance_valid(follow_this):
		return

	print("reset camera")
	var direction = follow_this.global_transform.basis.z
	var offset = direction * follow_distance
	global_position = follow_this.global_position + offset
	global_position.y += follow_height
	global_transform = global_transform.looking_at(follow_this.global_position, Vector3.UP)
