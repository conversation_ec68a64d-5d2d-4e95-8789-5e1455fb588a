[gd_resource type="VisualShader" load_steps=22 format=3 uid="uid://58v8kr6gx4wv"]

[ext_resource type="Texture2D" uid="uid://e85gd4w4bptt" path="res://assets/characters/Ghostrider/Artboard 4.png" id="3_ch5np"]
[ext_resource type="Texture2D" uid="uid://c840yn5hys0m4" path="res://assets/characters/Ghostrider/Artboard 3.png" id="4_vyerp"]

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_evvef"]
output_port_for_preview = 0
default_input_values = [0, 0.0, 1, 3.0]
operator = 3

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_h3f13"]
output_port_for_preview = 0

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_te6ai"]
input_name = "time"

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_6p1pp"]
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 1)]
op_type = 0
operator = 2

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_6o8uf"]
texture = ExtResource("3_ch5np")

[sub_resource type="Gradient" id="Gradient_d81a7"]

[sub_resource type="GradientTexture2D" id="GradientTexture2D_p5qhc"]
gradient = SubResource("Gradient_d81a7")
fill_from = Vector2(0, 1)
fill_to = Vector2(0, 0)

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_1wtw3"]
output_port_for_preview = 0
texture = SubResource("GradientTexture2D_p5qhc")

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_tiphw"]
operator = 2

[sub_resource type="VisualShaderNodeVectorDecompose" id="VisualShaderNodeVectorDecompose_6kyc4"]
default_input_values = [0, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeVec2Parameter" id="VisualShaderNodeVec2Parameter_irohw"]
parameter_name = "Vector2Parameter"

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_v6btc"]
parameter_name = "ColorParameter"

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_m4frg"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2
operator = 2

[sub_resource type="VisualShaderNodeVectorDecompose" id="VisualShaderNodeVectorDecompose_yr0tg"]
default_input_values = [0, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_c4che"]
output_port_for_preview = 0
texture = ExtResource("4_vyerp")

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_of5ux"]
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_byaly"]
input_name = "uv"

[sub_resource type="VisualShaderNodeVectorDecompose" id="VisualShaderNodeVectorDecompose_3fbnb"]
output_port_for_preview = 0
default_input_values = [0, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeBillboard" id="VisualShaderNodeBillboard_5f3h8"]
billboard_type = 3

[resource]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_disabled, diffuse_lambert, specular_schlick_ggx, unshaded;

uniform vec2 Vector2Parameter;
uniform sampler2D tex_frg_6;
uniform sampler2D tex_frg_23;
uniform sampler2D tex_frg_2;
uniform vec4 ColorParameter : source_color;



void vertex() {
	mat4 n_out2p0;
// GetBillboardMatrix:2
	{
		mat4 __wm = mat4(normalize(INV_VIEW_MATRIX[0]), normalize(INV_VIEW_MATRIX[1]), normalize(INV_VIEW_MATRIX[2]), MODEL_MATRIX[3]);
		__wm = __wm * mat4(vec4(cos(INSTANCE_CUSTOM.x), -sin(INSTANCE_CUSTOM.x), 0.0, 0.0), vec4(sin(INSTANCE_CUSTOM.x), cos(INSTANCE_CUSTOM.x), 0.0, 0.0), vec4(0.0, 0.0, 1.0, 0.0), vec4(0.0, 0.0, 0.0, 1.0));
		n_out2p0 = VIEW_MATRIX * __wm;
	}


// Output:0
	MODELVIEW_MATRIX = n_out2p0;


}

void fragment() {
// Input:8
	vec2 n_out8p0 = UV;


// Input:14
	float n_out14p0 = TIME;


// Vector2Parameter:27
	vec2 n_out27p0 = Vector2Parameter;


// VectorOp:15
	vec2 n_out15p0 = vec2(n_out14p0) * n_out27p0;


// UVFunc:13
	vec2 n_in13p1 = vec2(1.00000, 1.00000);
	vec2 n_out13p0 = n_out15p0 * n_in13p1 + UV;


// Texture2D:6
	vec4 n_out6p0 = texture(tex_frg_6, n_out13p0);


// VectorDecompose:9
	float n_out9p0 = n_out6p0.x;
	float n_out9p1 = n_out6p0.y;
	float n_out9p2 = n_out6p0.z;
	float n_out9p3 = n_out6p0.w;


// FloatOp:12
	float n_in12p1 = 3.00000;
	float n_out12p0 = n_out9p0 / n_in12p1;


// Texture2D:23
	vec4 n_out23p0 = texture(tex_frg_23, UV);


// VectorDecompose:25
	float n_out25p0 = n_out23p0.x;
	float n_out25p1 = n_out23p0.y;
	float n_out25p2 = n_out23p0.z;
	float n_out25p3 = n_out23p0.w;


// FloatOp:24
	float n_out24p0 = n_out12p0 * n_out25p0;


// VectorOp:7
	vec2 n_out7p0 = n_out8p0 + vec2(n_out24p0);


// Texture2D:2
	vec4 n_out2p0 = texture(tex_frg_2, n_out7p0);


// ColorParameter:28
	vec4 n_out28p0 = ColorParameter;


// VectorOp:4
	vec4 n_out4p0 = n_out2p0 * n_out28p0;


// VectorDecompose:5
	float n_out5p0 = n_out2p0.x;
	float n_out5p1 = n_out2p0.y;
	float n_out5p2 = n_out2p0.z;
	float n_out5p3 = n_out2p0.w;


// Output:0
	ALBEDO = vec3(n_out4p0.xyz);
	ALPHA = n_out5p0;


}
"
modes/cull = 2
flags/unshaded = true
nodes/vertex/2/node = SubResource("VisualShaderNodeBillboard_5f3h8")
nodes/vertex/2/position = Vector2(-80, 440)
nodes/vertex/connections = PackedInt32Array(2, 0, 0, 10)
nodes/fragment/2/node = SubResource("VisualShaderNodeTexture_6o8uf")
nodes/fragment/2/position = Vector2(-740, 80)
nodes/fragment/4/node = SubResource("VisualShaderNodeVectorOp_m4frg")
nodes/fragment/4/position = Vector2(100, 100)
nodes/fragment/5/node = SubResource("VisualShaderNodeVectorDecompose_yr0tg")
nodes/fragment/5/position = Vector2(0, 580)
nodes/fragment/6/node = SubResource("VisualShaderNodeTexture_c4che")
nodes/fragment/6/position = Vector2(-2020, 40)
nodes/fragment/7/node = SubResource("VisualShaderNodeVectorOp_of5ux")
nodes/fragment/7/position = Vector2(-1080, 140)
nodes/fragment/8/node = SubResource("VisualShaderNodeInput_byaly")
nodes/fragment/8/position = Vector2(-1620, 60)
nodes/fragment/9/node = SubResource("VisualShaderNodeVectorDecompose_3fbnb")
nodes/fragment/9/position = Vector2(-1680, 420)
nodes/fragment/12/node = SubResource("VisualShaderNodeFloatOp_evvef")
nodes/fragment/12/position = Vector2(-1380, 500)
nodes/fragment/13/node = SubResource("VisualShaderNodeUVFunc_h3f13")
nodes/fragment/13/position = Vector2(-2480, 120)
nodes/fragment/14/node = SubResource("VisualShaderNodeInput_te6ai")
nodes/fragment/14/position = Vector2(-3060, 160)
nodes/fragment/15/node = SubResource("VisualShaderNodeVectorOp_6p1pp")
nodes/fragment/15/position = Vector2(-2760, 360)
nodes/fragment/23/node = SubResource("VisualShaderNodeTexture_1wtw3")
nodes/fragment/23/position = Vector2(-1580, 880)
nodes/fragment/24/node = SubResource("VisualShaderNodeFloatOp_tiphw")
nodes/fragment/24/position = Vector2(-1000, 840)
nodes/fragment/25/node = SubResource("VisualShaderNodeVectorDecompose_6kyc4")
nodes/fragment/25/position = Vector2(-1300, 1000)
nodes/fragment/27/node = SubResource("VisualShaderNodeVec2Parameter_irohw")
nodes/fragment/27/position = Vector2(-3340, 360)
nodes/fragment/28/node = SubResource("VisualShaderNodeColorParameter_v6btc")
nodes/fragment/28/position = Vector2(-260, 260)
nodes/fragment/connections = PackedInt32Array(2, 0, 4, 0, 4, 0, 0, 0, 5, 0, 0, 1, 2, 0, 5, 0, 8, 0, 7, 0, 7, 0, 2, 0, 9, 0, 12, 0, 14, 0, 15, 0, 6, 0, 9, 0, 12, 0, 24, 0, 23, 0, 25, 0, 25, 0, 24, 1, 24, 0, 7, 1, 13, 0, 6, 0, 27, 0, 15, 1, 15, 0, 13, 2, 28, 0, 4, 1)
