[gd_scene load_steps=25 format=3 uid="uid://cxaypidrqv56e"]

[ext_resource type="Script" path="res://Scenes/FreeRide/Assets/Buildings/Bank/ATM.gd" id="1_yvbjb"]
[ext_resource type="PackedScene" uid="uid://cmdaistopeoan" path="res://Scenes/FreeRide/Assets/Actionables/BaseActionable.tscn" id="2_vv26w"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="3_gdsei"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="4_fc3b3"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="5_wfi5d"]
[ext_resource type="Texture2D" uid="uid://cdi8kfb3vhhfs" path="res://Scenes/ui/assets/button-back.png" id="6_cke01"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Assets/Buildings/Bank/TabOnline.gd" id="7_kwkg6"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Assets/Buildings/Bank/TransactionParent.gd" id="8_g5y71"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Assets/Buildings/Bank/TabUsername.gd" id="9_5ueyx"]
[ext_resource type="StyleBox" uid="uid://cyubbwfeoqdsn" path="res://Scenes/ui/WhiteLineEdit.tres" id="9_luxy6"]
[ext_resource type="Texture2D" uid="uid://cfg3qf3hhytrh" path="res://Scenes/ui/assets/whitebutton.png" id="10_0iyfh"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="10_o1oiy"]
[ext_resource type="Texture2D" uid="uid://dumng7vsvpcyd" path="res://Scenes/ui/assets/icon-copy.png" id="11_hiupm"]
[ext_resource type="PackedScene" uid="uid://dfw3vd3t4jtva" path="res://Scenes/ui/coin_show.tscn" id="11_n3y8f"]
[ext_resource type="PackedScene" uid="uid://c24xuvloqf3hu" path="res://Scenes/FreeRide/Assets/Buildings/Bank/Sources/ATMModel.tscn" id="12_offwo"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Assets/Buildings/Bank/TabHistory.gd" id="14_o5xmp"]
[ext_resource type="Theme" uid="uid://cavx3qitdfs8s" path="res://Scenes/ui/ui_theme.tres" id="15_lsa2t"]
[ext_resource type="Texture2D" uid="uid://dk1yd3uc4idav" path="res://Scenes/FreeRide/Assets/Actionables/Misc/Signs/Aberbank-8.png" id="18_h6mk6"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ssxxh"]
size = Vector3(2.44312, 4.06513, 3.48435)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_r0pco"]
texture = ExtResource("3_gdsei")

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4eavd"]
bg_color = Color(0, 0.168627, 0, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.278431, 0.278431, 0.278431, 1)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7358v"]
bg_color = Color(0, 0.788235, 0, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.278431, 0.278431, 0.278431, 1)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_owhnq"]
bg_color = Color(0.34902, 0.592157, 0, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t7ers"]
bg_color = Color(0, 0, 0, 0.784314)

[node name="ATM" type="Node3D"]
script = ExtResource("1_yvbjb")

[node name="Mesh" parent="." instance=ExtResource("12_offwo")]

[node name="ActionableArea" parent="." instance=ExtResource("2_vv26w")]

[node name="CollisionShape3D3" type="CollisionShape3D" parent="ActionableArea"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.1849, 0.589715)
shape = SubResource("BoxShape3D_ssxxh")

[node name="hud" type="Control" parent="."]
layout_direction = 2
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="hud"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_r0pco")

[node name="Text" type="Label" parent="hud"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 31.0
offset_bottom = 82.0
grow_horizontal = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 30
text = "BANK_TITLE"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 1

[node name="ExitButton" parent="hud" instance=ExtResource("5_wfi5d")]
custom_minimum_size = Vector2(50, 50)
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 56.0
offset_top = 20.0
offset_right = 138.0
offset_bottom = 93.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(53, 49)

[node name="TextureRect" type="TextureRect" parent="hud/ExitButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 7.0
offset_right = 7.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_cke01")
expand_mode = 1
stretch_mode = 4
metadata/_edit_lock_ = true

[node name="HBoxContainer" type="HBoxContainer" parent="hud"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 120.0
offset_bottom = 187.0
grow_horizontal = 2
alignment = 1

[node name="OnlineTabButton" parent="hud/HBoxContainer" instance=ExtResource("5_wfi5d")]
custom_minimum_size = Vector2(350, 50)
layout_mode = 2

[node name="Unselected" type="Panel" parent="hud/HBoxContainer/OnlineTabButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_4eavd")

[node name="Selected" type="Panel" parent="hud/HBoxContainer/OnlineTabButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_7358v")

[node name="text" type="Label" parent="hud/HBoxContainer/OnlineTabButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 25
text = "BANK_TAB_ONLINE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="UsernameTabButton" parent="hud/HBoxContainer" instance=ExtResource("5_wfi5d")]
custom_minimum_size = Vector2(350, 50)
layout_mode = 2

[node name="Unselected" type="Panel" parent="hud/HBoxContainer/UsernameTabButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_4eavd")

[node name="Selected" type="Panel" parent="hud/HBoxContainer/UsernameTabButton"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_7358v")

[node name="text" type="Label" parent="hud/HBoxContainer/UsernameTabButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 25
text = "BANK_TAB_USERNAME"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HistoryTabButton" parent="hud/HBoxContainer" instance=ExtResource("5_wfi5d")]
custom_minimum_size = Vector2(350, 50)
layout_mode = 2

[node name="Unselected" type="Panel" parent="hud/HBoxContainer/HistoryTabButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_4eavd")

[node name="Selected" type="Panel" parent="hud/HBoxContainer/HistoryTabButton"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_7358v")

[node name="text" type="Label" parent="hud/HBoxContainer/HistoryTabButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 25
text = "BANK_TAB_HISTORY"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TabOnline" type="Control" parent="hud"]
visible = false
custom_minimum_size = Vector2(1053, 500)
layout_mode = 1
anchors_preset = 13
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -522.0
offset_top = 200.0
offset_right = 531.0
offset_bottom = -19.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("7_kwkg6")

[node name="ScrollContainer" type="ScrollContainer" parent="hud/TabOnline"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
horizontal_scroll_mode = 0
scroll_deadzone = 60

[node name="VBoxContainer" type="VBoxContainer" parent="hud/TabOnline/ScrollContainer"]
layout_mode = 2

[node name="TabUsername" type="Control" parent="hud"]
visible = false
custom_minimum_size = Vector2(1053, 500)
layout_mode = 1
anchors_preset = 13
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -522.0
offset_top = 200.0
offset_right = 531.0
offset_bottom = -19.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("9_5ueyx")

[node name="ScrollContainer" type="ScrollContainer" parent="hud/TabUsername"]
custom_minimum_size = Vector2(500, 300)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -253.5
offset_top = 18.0
offset_right = 254.5
offset_bottom = 500.0
grow_horizontal = 2
focus_mode = 2
horizontal_scroll_mode = 0
scroll_deadzone = 60

[node name="VBoxContainer" type="VBoxContainer" parent="hud/TabUsername/ScrollContainer"]
layout_mode = 2
theme_override_constants/separation = 20
alignment = 2

[node name="MyUsername" parent="hud/TabUsername/ScrollContainer/VBoxContainer" instance=ExtResource("5_wfi5d")]
custom_minimum_size = Vector2(200, 100)
layout_mode = 2

[node name="TextureRect2" type="NinePatchRect" parent="hud/TabUsername/ScrollContainer/VBoxContainer/MyUsername"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("10_0iyfh")
patch_margin_left = 32
patch_margin_top = 23
patch_margin_right = 29
patch_margin_bottom = 26

[node name="CopyImage" type="TextureRect" parent="hud/TabUsername/ScrollContainer/VBoxContainer/MyUsername"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -66.0
offset_top = -25.0
offset_right = -21.0
offset_bottom = 25.0
grow_horizontal = 0
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("11_hiupm")
stretch_mode = 4

[node name="UsernameLabel" type="Label" parent="hud/TabUsername/ScrollContainer/VBoxContainer/MyUsername"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 16.0
offset_right = -77.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 4
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 31
text = "username"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Seprator" type="Control" parent="hud/TabUsername/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2

[node name="LineEdit" type="LineEdit" parent="hud/TabUsername/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(500, 80)
layout_mode = 2
theme_override_colors/selection_color = Color(0, 0, 0, 1)
theme_override_colors/caret_color = Color(0, 0, 0, 1)
theme_override_colors/font_placeholder_color = Color(0.858824, 0.858824, 0.858824, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_colors/font_selected_color = Color(0.243137, 0.243137, 0.243137, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_constants/outline_size = 6
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = ExtResource("9_luxy6")
theme_override_styles/normal = ExtResource("9_luxy6")
placeholder_text = "BANK_RECEIVER_USERNAME"
alignment = 1
max_length = 12
select_all_on_focus = true

[node name="SearchButton" parent="hud/TabUsername/ScrollContainer/VBoxContainer" instance=ExtResource("5_wfi5d")]
custom_minimum_size = Vector2(200, 50)
layout_mode = 2
size_flags_horizontal = 4

[node name="Selected" type="Panel" parent="hud/TabUsername/ScrollContainer/VBoxContainer/SearchButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_owhnq")

[node name="text" type="Label" parent="hud/TabUsername/ScrollContainer/VBoxContainer/SearchButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 25
text = "BANK_SEARCH"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HTTPRequest" type="HTTPRequest" parent="hud/TabUsername"]

[node name="LoadingElement" parent="hud/TabUsername" instance=ExtResource("10_o1oiy")]
visible = false
custom_minimum_size = Vector2(45, 45)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -22.5001
offset_top = 296.0
offset_right = 22.4999
offset_bottom = 341.0
grow_vertical = 1

[node name="TabHistory" type="Control" parent="hud"]
custom_minimum_size = Vector2(1053, 500)
layout_mode = 1
anchors_preset = 13
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -522.0
offset_top = 200.0
offset_right = 531.0
offset_bottom = -19.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("14_o5xmp")

[node name="ScrollContainer" type="ScrollContainer" parent="hud/TabHistory"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("15_lsa2t")
horizontal_scroll_mode = 0
scroll_deadzone = 60

[node name="VBoxContainer" type="VBoxContainer" parent="hud/TabHistory/ScrollContainer"]
layout_mode = 2

[node name="HTTPRequest" type="HTTPRequest" parent="hud/TabHistory"]

[node name="LoadingElement" parent="hud/TabHistory" instance=ExtResource("10_o1oiy")]
custom_minimum_size = Vector2(45, 45)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -22.5
offset_top = -22.5
offset_right = 22.5
offset_bottom = 22.5

[node name="EmptyLabel" type="Label" parent="hud/TabHistory"]
layout_mode = 1
anchors_preset = 14
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_top = -25.5
offset_bottom = 25.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 30
text = "BANK_EMPTY"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 1

[node name="TransactionParent" type="Control" parent="hud"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("8_g5y71")

[node name="Panel" type="Panel" parent="hud/TransactionParent"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_r0pco")

[node name="Text" type="Label" parent="hud/TransactionParent"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 31.0
offset_bottom = 82.0
grow_horizontal = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 30
text = "BANK_TITLE"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 1

[node name="TransactionExitButton" parent="hud/TransactionParent" instance=ExtResource("5_wfi5d")]
custom_minimum_size = Vector2(50, 50)
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 56.0
offset_top = 20.0
offset_right = 138.0
offset_bottom = 93.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(53, 49)

[node name="TextureRect" type="TextureRect" parent="hud/TransactionParent/TransactionExitButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 7.0
offset_right = 7.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_cke01")
expand_mode = 1
stretch_mode = 4
metadata/_edit_lock_ = true

[node name="ResultLabel" type="Label" parent="hud/TransactionParent"]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -14.5
offset_top = -25.5
offset_right = 14.5
offset_bottom = 25.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 30
text = "Success"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ScrollContainer" type="ScrollContainer" parent="hud/TransactionParent"]
custom_minimum_size = Vector2(500, 300)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -250.0
offset_top = 144.0
offset_right = 250.0
offset_bottom = 690.0
grow_horizontal = 2
focus_mode = 2
horizontal_scroll_mode = 0
scroll_deadzone = 60

[node name="VBoxContainer" type="VBoxContainer" parent="hud/TransactionParent/ScrollContainer"]
layout_mode = 2
alignment = 2

[node name="RecevierLabel" type="Label" parent="hud/TransactionParent/ScrollContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 30
text = "Receiver"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LineEdit" type="LineEdit" parent="hud/TransactionParent/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(500, 80)
layout_mode = 2
theme_override_colors/selection_color = Color(0, 0, 0, 1)
theme_override_colors/caret_color = Color(0, 0, 0, 1)
theme_override_colors/font_placeholder_color = Color(0.858824, 0.858824, 0.858824, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_colors/font_selected_color = Color(0.243137, 0.243137, 0.243137, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_constants/outline_size = 6
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = ExtResource("9_luxy6")
theme_override_styles/normal = ExtResource("9_luxy6")
placeholder_text = "BANK_AMOUNT"
alignment = 1
max_length = 7
virtual_keyboard_type = 2
select_all_on_focus = true

[node name="FeeLabel" type="Label" parent="hud/TransactionParent/ScrollContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 30
text = "10"
horizontal_alignment = 1
vertical_alignment = 1

[node name="SendButton" parent="hud/TransactionParent/ScrollContainer/VBoxContainer" instance=ExtResource("5_wfi5d")]
custom_minimum_size = Vector2(200, 50)
layout_mode = 2
size_flags_horizontal = 4

[node name="Selected" type="Panel" parent="hud/TransactionParent/ScrollContainer/VBoxContainer/SendButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_owhnq")

[node name="text" type="Label" parent="hud/TransactionParent/ScrollContainer/VBoxContainer/SendButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 25
text = "BANK_SEND2"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingElement" parent="hud/TransactionParent" instance=ExtResource("10_o1oiy")]
visible = false
custom_minimum_size = Vector2(45, 45)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -22.5
offset_top = -22.5
offset_right = 22.5
offset_bottom = 22.5

[node name="HTTPRequest" type="HTTPRequest" parent="hud/TransactionParent"]

[node name="CoinShow" parent="hud" instance=ExtResource("11_n3y8f")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -230.0
offset_top = 32.0
offset_right = -80.0
offset_bottom = 82.0
grow_horizontal = 0
grow_vertical = 1
link_to_shop = false

[node name="Locked" type="Panel" parent="hud"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 108.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_t7ers")

[node name="Text" type="Label" parent="hud/Locked"]
layout_mode = 1
anchors_preset = 14
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_top = -25.5
offset_bottom = 25.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("4_fc3b3")
theme_override_font_sizes/font_size = 30
text = "CUP"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 1

[node name="Sprite3D" type="Sprite3D" parent="." groups=["VisibleGroup0"]]
transform = Transform3D(0.1, 0, 0, 0, 0.07, 0, 0, 0, 0.07, -0.0313029, 3.5345, 0.927132)
modulate = Color(0.183405, 0.270097, 0.707725, 1)
texture = ExtResource("18_h6mk6")

[connection signal="action" from="ActionableArea" to="." method="_on_actionable_area_action"]
[connection signal="pressed" from="hud/ExitButton" to="." method="exit"]
[connection signal="pressed" from="hud/HBoxContainer/OnlineTabButton" to="." method="_on_online_tab_button_pressed"]
[connection signal="pressed" from="hud/HBoxContainer/UsernameTabButton" to="." method="_on_username_tab_button_pressed"]
[connection signal="pressed" from="hud/HBoxContainer/HistoryTabButton" to="." method="_on_history_tab_button_pressed"]
[connection signal="pressed" from="hud/TabUsername/ScrollContainer/VBoxContainer/MyUsername" to="hud/TabUsername" method="_on_my_username_pressed"]
[connection signal="pressed" from="hud/TabUsername/ScrollContainer/VBoxContainer/SearchButton" to="hud/TabUsername" method="_on_search_button_pressed"]
[connection signal="request_completed" from="hud/TabUsername/HTTPRequest" to="hud/TabUsername" method="_on_http_request_request_completed"]
[connection signal="request_completed" from="hud/TabHistory/HTTPRequest" to="hud/TabHistory" method="_on_http_request_request_completed"]
[connection signal="pressed" from="hud/TransactionParent/TransactionExitButton" to="hud/TransactionParent" method="_on_transaction_exit_button_pressed"]
[connection signal="text_changed" from="hud/TransactionParent/ScrollContainer/VBoxContainer/LineEdit" to="hud/TransactionParent" method="_on_line_edit_text_changed"]
[connection signal="pressed" from="hud/TransactionParent/ScrollContainer/VBoxContainer/SendButton" to="hud/TransactionParent" method="_on_send_button_pressed"]
[connection signal="request_completed" from="hud/TransactionParent/HTTPRequest" to="hud/TransactionParent" method="_on_http_request_request_completed"]
