extends FiniteStateMachine
class_name VehicleStateMachine

@onready var vehicle_race: VehicleRace = $".."
@onready var LobbyState = $Lobby
@onready var InGameState = $InGame
@onready var ResultsState = $Results

@onready var sit_parent: Node3D = $"../SitParent"
@onready var exit_place: Marker3D = $"../ExitPlace"
@onready var result_container: VBoxContainer = $"../ResultHUD/ScrollContainer/VBoxContainer"
@onready var result_hud: Panel = $"../ResultHUD"
@onready var reset_button: Control = $InGame/HUD/ResetButton


@onready var states_list =[LobbyState, InGameState, ResultsState]

var MAX_CHECKPOINTS = 6
var STATE_SYNC_TIME = 1 / 5.0#2 times per second
var server_turn = 0#Zero Base
var players = [null, null, null, null]#Both server and client
var players_smart = [-1, -1, -1, -1]
var players_checkpoint = [0, 0, 0, 0]
var finished_count = 0
var ranks = []
var ingame_start_players = 0
var start_time = 0
var time_counter = 0
var INGAME_TIME = 180#Total Time

var client_im_in = false
var client_checkpoint = 0

func _ready():
	if Constants.is_server:
		current_state = LobbyState


func reset_game():
	if current_state == InGameState:
		print("Finishing Game!")
		current_state = ResultsState
		return
	current_state = LobbyState
	client_im_in = false


func _process(delta):
	super(delta)
	check_for_dc_players()
	send_state_to_players(delta)
	if Constants.is_client():
		reset_button.visible = client_checkpoint > 0


#server
func get_current_state_int():
	var index = 0
	for state in states_list:
		if state == current_state:
			return index
		index += 1
	return 0


#client
func set_state_by_int(state_int):
	if state_int < 0 or state_int >= states_list.size():
		return
	current_state = states_list[state_int]


@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_MINIGAMES)
func on_player_enter(smart):
	if current_state != LobbyState:
		Constants.show_toast_tr.rpc_id(multiplayer.get_remote_sender_id(), "WAIT_TILL_FINISH")
		return
	
	var index = find_empty_player()
	if index == null:
		Constants.show_toast_tr.rpc_id(multiplayer.get_remote_sender_id(), "WAIT_TILL_FINISH")
		return
	
	
	for p in players:
		if p == multiplayer.get_remote_sender_id():
			return
	
	players[index] = multiplayer.get_remote_sender_id()
	players_smart[index] = smart
	i_joined.rpc_id(multiplayer.get_remote_sender_id(), index)
	if player_count() > 1:
		if not LobbyState.countdown:
			LobbyState.start_count_down()


#Server
@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_MINIGAMES)
func on_player_exit():
	#if turn < 0 or turn > 3:
		#i_exited.rpc_id(multiplayer.get_remote_sender_id())
		#return
	var playerId = multiplayer.get_remote_sender_id()
	for index in players.size():
		if players[index] == playerId:
			players[index] = null
			i_exited.rpc_id(multiplayer.get_remote_sender_id())
			if player_count() == 0:
				reset_game()
			else:
				if current_state == LobbyState:
					if player_count() == 1:
						LobbyState.stop_count_down()
			return
	
	i_exited.rpc_id(multiplayer.get_remote_sender_id())


@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_MINIGAMES)
func checkpoint(checkpoint_id):
	if current_state != InGameState:
		return
	
	var playerId = multiplayer.get_remote_sender_id()
	for index in players.size():
		if players[index] == playerId:
			if players_checkpoint[index] + 1 == checkpoint_id:
				#Checkpoint reached!
				players_checkpoint[index] = checkpoint_id
				checkpoint_received.rpc_id(playerId, checkpoint_id)
			
			if players_checkpoint[index] == MAX_CHECKPOINTS:
				server_player_finished(playerId)
				players[index] = null


#**********************************Client RPCs*********************************#
#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func i_joined(turn):#Turn is 0 base
	#Selector.on_sit(true)
	client_im_in = true
	client_checkpoint = 0
	var freeride_scene = Constants.server.game_scene as FreeRide
	var sit = sit_parent.get_child(turn) as SitPosition
	freeride_scene.on_joined_minigame()
	#freeride_scene.player.global_position = sit.get_sit_position()
	#freeride_scene.player.look_at(sit.get_look_position())
	#freeride_scene.player.velocity = Vector3(0, 0, 0)
	#freeride_scene.player.disable_gravity()
	#freeride_scene.player.freezeState.start_state(freeride_scene.player.SIT_ANIMATION, 1000 * 1000 * 1000, "___")
	
	var vehicle = VehicleManager.my_vehicle() as MyVehicle
	vehicle.global_position = sit.get_sit_position()
	vehicle.global_rotation = sit.global_rotation
	vehicle.global_rotation.y *= -1
	vehicle.linear_velocity = Vector3(0, 0, 0)
	vehicle.should_stop = true
	vehicle_race.my_vehicle_at_start = vehicle
	freeride_scene.reset_vehicle_camera()
	
	if current_state != LobbyState:
		current_state = LobbyState
	else:
		LobbyState.on_enter()


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func i_exited():
	ClientBackendManager.send_amiban_request()
	#hangout.exit()
	#Selector.on_exit_sit()
	client_im_in = false
	LobbyState.hud.visible = false
	LobbyState.hud.process_mode = Node.PROCESS_MODE_DISABLED
	InGameState.hud.visible = false
	InGameState.hud.process_mode = Node.PROCESS_MODE_DISABLED
	var freeride_scene = Constants.server.game_scene as FreeRide
	var vehicle = VehicleManager.my_vehicle() as MyVehicle
	if is_instance_valid(vehicle):
		vehicle.global_position = exit_place.global_position
		vehicle.global_rotation = exit_place.global_rotation
		vehicle.global_rotation.y *= -1
		vehicle.linear_velocity = Vector3(0, 0, 0)
		vehicle.should_stop = false
		vehicle.freeze(0.1)
		Constants.client.my_player_scene.global_position = exit_place.global_position
	vehicle_race.my_vehicle_at_start = null
	freeride_scene.on_exit_minigame()
	freeride_scene.vehicle_camera3d.make_current()
	freeride_scene.on_mount_vehicle()
	freeride_scene.reset_vehicle_camera()
	DataSaver.send_save_request()


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func checkpoint_received(checkpoint_id):
	SoundManager.play_sound(SoundManager.SOUND_TYPE.Checkpoint)
	client_checkpoint = checkpoint_id


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func update_state(buffer:PackedByteArray):
	set_state_by_int(buffer.decode_u8(0))
	set_player_id(0, buffer.decode_u32(1))
	set_player_id(1, buffer.decode_u32(5))
	set_player_id(2, buffer.decode_u32(9))
	set_player_id(3, buffer.decode_u32(13))
	current_state.decode(17, buffer)
	#player1_profile.set_id(get_player_id(0))
	#player2_profile.set_id(get_player_id(1))
	#player3_profile.set_id(get_player_id(2))
	#player4_profile.set_id(get_player_id(3))
	#player1_profile.set_smart(buffer.decode_s16(17))
	#player2_profile.set_smart(buffer.decode_s16(19))
	#player3_profile.set_smart(buffer.decode_s16(21))
	#player4_profile.set_smart(buffer.decode_s16(23))


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func times_up():
	Constants.show_toast(tr(""))
	i_exited()


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func current_result(ranks_data):
	print("results: ", ranks_data)
	for ch in result_container.get_children():
		ch.queue_free()
	for rank in ranks_data:
		var row = preload("res://Scenes/FreeRide/Assets/Buildings/RaceMap/VehicleRaceRankRow.tscn").instantiate()
		result_container.add_child(row)
		row.set_data(rank)
	result_hud.visible = true
	i_exited()
	await Constants.wait_timer(10)
	result_hud.visible = false


#Server
func on_times_up():
	for p in players:
		if p != null:
			times_up.rpc_id(p)
	current_state = ResultsState


func check_for_dc_players():
	if Constants.is_client():
		return
		
	if current_state == LobbyState:
		return

	var index = 0
	for p in players:
		if p != null:
			if Constants.server.is_dc(p):
				players[index] = null
		index += 1
	if player_count() == 0:
		reset_game()


func find_empty_player():
	var index = 0
	for p in players:
		if p == null:
			return index
		index += 1
	
	return null


func player_count():
	var count = 0
	for p in players:
		if p != null:
			count += 1
	
	return count


var state_counter = 0
#Server
func send_state_to_players(delta:float):
	if Constants.is_client():
		return

	state_counter += delta
	if state_counter >= STATE_SYNC_TIME:
		state_counter = 0
		var buffer = PackedByteArray()
		buffer.resize(17)
		buffer.encode_u8(0, get_current_state_int())
		buffer.encode_u32(1, get_player_id(0))
		buffer.encode_u32(5, get_player_id(1))
		buffer.encode_u32(9, get_player_id(2))
		buffer.encode_u32(13, get_player_id(3))
		#buffer.encode_s16(17, players_smart[0])
		#buffer.encode_s16(19, players_smart[1])
		#buffer.encode_s16(21, players_smart[2])
		#buffer.encode_s16(23, players_smart[3])
		buffer = current_state.encode(buffer)
		for p in players:
			if p != null:
				if not Constants.server.is_dc(p):
					update_state.rpc_id(p, buffer)


#Server
func get_player_id(turn):
	if players[turn] == null:
		return 0
	return players[turn]


#Client
func set_player_id(turn, id):
	if id == 0:
		players[turn] = null
	else:
		players[turn] = id


func finish_game():
	pass


func server_player_finished(playerId):
	var found = false
	for index in players.size():
		var p = players[index]
		if p == playerId:
			players[index] = null
			found = true
	if not found:
		return

	finished_count += 1
	var data = {
		"backend_id": Constants.server.players_data[playerId]["backend_id"],
		"rank": finished_count,
		"time": Time.get_ticks_msec() - start_time,
		"handle": Constants.server.players_data[playerId]["selection"]["name"]
 	}
	data["smart"] = ingame_start_players - data["rank"]
	match ingame_start_players:
		1: data["coin"] = 0
		2: data["coin"] = Selector.vehicle_race_2player_prize
		3: data["coin"] = Selector.vehicle_race_3player_prize
		4: data["coin"] = Selector.vehicle_race_4player_prize
	data["coin"] = clamp(data["coin"] - (data["rank"] - 1) * 70, 0, data["coin"])
	ranks.append(data)
	
	current_result.rpc_id(playerId, ranks)
