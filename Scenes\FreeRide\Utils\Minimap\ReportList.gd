extends Panel

@onready var list_http_request: HTTPRequest = $ListHTTPRequest
@onready var loading: Panel = $Loading
@onready var container: VBoxContainer = $ScrollContainer/VBoxContainer
@onready var details: Panel = $Details
@onready var detail_text: Label = $Details/Text


func _ready() -> void:
	exit()


func show_popup(username):
	visible = true
	loading.visible = true
	details.visible = false
	var url = Constants.BACKEND_URL + "/game/free_admin_report_list/"
	var data = {
		"username": username,
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	list_http_request.cancel_request()
	list_http_request.request(url, headers, HTTPClient.METHOD_POST, json)
	for ch in container.get_children():
		ch.queue_free()


func _on_list_http_request_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray) -> void:
	loading.visible = false
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		if len(json["reports"]) == 0:
			Constants.show_toast("سابقه گزارش ندارد")
			exit()
		else:
			for report in json["reports"]:
				var row = preload("res://Scenes/FreeRide/Utils/Minimap/PoliceReportRow.tscn").instantiate()
				container.add_child(row)
				row.set_data(report)
				row.pressed.connect(on_report_pressed)
	else:
		Constants.show_toast(str(response_code))
		exit()


func exit():
	visible = false


func on_report_pressed(text):
	details.visible = true
	detail_text.text = text


func _on_details_cancel_button_pressed() -> void:
	details.visible = false
