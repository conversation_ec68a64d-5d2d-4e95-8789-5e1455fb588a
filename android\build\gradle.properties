# Godot gradle build settings.
# These properties apply when running a gradle build from the Godot editor.
# NOTE: This should be kept in sync with 'godot/platform/android/java/gradle.properties' except
# where otherwise specified.

# For more details on how to configure your build environment visit
# https://www.gradle.org/docs/current/userguide/build_environment.html

android.enableJetifier=true
android.useAndroidX=true

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4536m

# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# https://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

org.gradle.warning.mode=all

# Enable resource optimizations for release build.
# NOTE: This is turned off for template release build in order to support the build legacy process.
android.enableResourceOptimizations=true

# Fix gradle build errors when the build path contains non-ASCII characters
android.overridePathCheck=true
