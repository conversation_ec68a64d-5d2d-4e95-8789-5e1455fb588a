[gd_scene load_steps=43 format=3 uid="uid://br645vs3dn0fs"]

[ext_resource type="Script" path="res://Scenes/FreeRide/free_ride.gd" id="1_xv4au"]
[ext_resource type="Script" path="res://Scenes/FreeRide/RemoteCharacters.gd" id="2_cm7ox"]
[ext_resource type="Script" path="res://Scenes/LobbyConnectManager.gd" id="2_r4oxd"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="3_ghefi"]
[ext_resource type="PackedScene" uid="uid://5boamgpjjlhs" path="res://Scenes/player/touch_controller.tscn" id="4_3i1bu"]
[ext_resource type="PackedScene" uid="uid://du1c3f3voorwq" path="res://Scenes/FreeRide/Assets/Misc/TimeCounter.tscn" id="4_f2s7h"]
[ext_resource type="PackedScene" uid="uid://c8afqwkno6d3w" path="res://Scenes/ui/emote_touch.tscn" id="5_lh883"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="6_y3tdy"]
[ext_resource type="PackedScene" uid="uid://cbs18n5wyvbrb" path="res://Scenes/ui/assets/HungerBar/WarmthBar.tscn" id="8_f5a71"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="9_m6asc"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="10_ied73"]
[ext_resource type="Texture2D" uid="uid://e31wea0v802m" path="res://Scenes/ui/assets/mobile/mobile.png" id="10_waqvo"]
[ext_resource type="PackedScene" uid="uid://dkxuohwu0ahaw" path="res://Scenes/ui/job/JobPanel.tscn" id="12_11jyk"]
[ext_resource type="Texture2D" uid="uid://dmy2u6qq14o8u" path="res://Scenes/ui/assets/buttonbg.png" id="12_hbof3"]
[ext_resource type="Texture2D" uid="uid://cdmdwso71qnjv" path="res://Scenes/ui/assets/mobile/call.png" id="12_tkg5p"]
[ext_resource type="Texture2D" uid="uid://cohtqfdbotmw5" path="res://Scenes/ui/assets/PNG/blue_button05.png" id="12_w5avr"]
[ext_resource type="PackedScene" uid="uid://cvnuejwsey787" path="res://Scenes/ui/exit_popup.tscn" id="12_wkxoy"]
[ext_resource type="PackedScene" uid="uid://xsiasxrluhpp" path="res://Scenes/ui/player_counter.tscn" id="13_2ootq"]
[ext_resource type="Texture2D" uid="uid://nlwq5bbyfh6x" path="res://Scenes/ui/assets/PNG/grey_button04.png" id="13_8pmws"]
[ext_resource type="PackedScene" uid="uid://b33sops2kc74v" path="res://Scenes/ui/HungerShow.tscn" id="13_db3us"]
[ext_resource type="Texture2D" uid="uid://dnnx7csw5cbta" path="res://Scenes/ui/assets/map2.png" id="13_gc0ya"]
[ext_resource type="PackedScene" uid="uid://bsf58c8bpxio0" path="res://Scenes/ui/FreeRideChat.tscn" id="14_5ir2c"]
[ext_resource type="Script" path="res://Scenes/player/CameraController.gd" id="14_vsc10"]
[ext_resource type="Texture2D" uid="uid://cuqpepb03tk6r" path="res://Scenes/ui/assets/pauseFreeride.png" id="16_3pydt"]
[ext_resource type="PackedScene" uid="uid://crek43chmkusr" path="res://Scenes/ui/CoinAdderUI.tscn" id="16_vnpdg"]
[ext_resource type="PackedScene" uid="uid://dm2q3pf8gnlfl" path="res://Scenes/FreeRide/Utils/Minimap/MiniMap.tscn" id="17_11yvo"]
[ext_resource type="Shader" path="res://Scenes/FreeRide/Assets/Actionables/Misc/Teleport/transition_shader.gdshader" id="17_xygam"]
[ext_resource type="Texture2D" uid="uid://c85bhmj02imxk" path="res://Scenes/ui/assets/ProgressBG.png" id="18_tii80"]
[ext_resource type="Script" path="res://Autoload/FreeRideMusicSelector.gd" id="20_xffcm"]
[ext_resource type="Script" path="res://Scenes/FreeRide/ClientReconnector.gd" id="23_jcnc1"]
[ext_resource type="StyleBox" uid="uid://3jmm6a0rtg6b" path="res://Scenes/ui/panel_background.tres" id="25_wttxw"]
[ext_resource type="PackedScene" uid="uid://1m80voldugd6" path="res://Inventory/UI/PlayerInventoryContainer.tscn" id="26_5ua6q"]
[ext_resource type="Texture2D" uid="uid://0pg6iqc4n45o" path="res://Scenes/ui/assets/yellow.png" id="26_ymo16"]
[ext_resource type="PackedScene" uid="uid://cdy6jtsocrivb" path="res://Scenes/Vehicle/Scenes/VehicleTouch.tscn" id="27_l2gfc"]
[ext_resource type="Script" path="res://Scenes/Vehicle/gvep/scripts/VehicleCamera.gd" id="29_qyomj"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8ikte"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_wpvqg"]
texture = ExtResource("9_m6asc")

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mtps7"]
bg_color = Color(0, 0, 0, 1)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_bkext"]
texture = ExtResource("9_m6asc")

[sub_resource type="SphereMesh" id="SphereMesh_fry5p"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_eh28b"]
shader = ExtResource("17_xygam")
shader_parameter/size = Vector2(64, 48)
shader_parameter/value = 1.0
shader_parameter/albedo_tex = ExtResource("18_tii80")

[sub_resource type="CanvasTexture" id="CanvasTexture_lh488"]

[node name="FreeRide" type="Node3D"]
script = ExtResource("1_xv4au")

[node name="Player" type="Node3D" parent="."]

[node name="Remotes" type="Node3D" parent="."]
script = ExtResource("2_cm7ox")

[node name="LobbyConnectManager" type="Node" parent="."]
script = ExtResource("2_r4oxd")

[node name="DayNightTimeCounter" parent="." instance=ExtResource("4_f2s7h")]
time = 60.0

[node name="HUD" type="Control" parent="."]
layout_direction = 2
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
metadata/_edit_lock_ = true

[node name="GridContainer" type="GridContainer" parent="HUD"]
layout_mode = 0
offset_left = 9.0
offset_right = 388.0
offset_bottom = 48.0
theme_override_constants/h_separation = 10
theme_override_constants/v_separation = -15
columns = 4

[node name="Ping" type="Label" parent="HUD/GridContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 25
text = "ping "
vertical_alignment = 1

[node name="Undercover" type="Label" parent="HUD/GridContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 25
text = "مخفی"

[node name="fps" type="Label" parent="HUD/GridContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 25
text = "FPS=30"

[node name="GraphicSettings" type="Label" parent="HUD/GridContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 25
text = "Graphics=1"

[node name="ActiveRemotes" type="Label" parent="HUD/GridContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 25
text = "ActiveRemotes=1"

[node name="PlayerCounter" parent="HUD" instance=ExtResource("13_2ootq")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -358.0
offset_top = 19.0
offset_right = -219.0
offset_bottom = 89.0
grow_horizontal = 0
grow_vertical = 1

[node name="VehicleTouch" parent="HUD" instance=ExtResource("27_l2gfc")]
visible = false
layout_mode = 1

[node name="TouchController" parent="HUD" instance=ExtResource("4_3i1bu")]
layout_mode = 1
visible_action = true
metadata/_edit_lock_ = true

[node name="PlayerInventoryContainer" parent="HUD" instance=ExtResource("26_5ua6q")]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
offset_left = -190.5
offset_top = -96.0
offset_right = 190.5
offset_bottom = -16.0
grow_vertical = 0

[node name="Warmth" parent="HUD" instance=ExtResource("8_f5a71")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -520.0
offset_top = 22.0
offset_right = -380.0
offset_bottom = 87.0
grow_horizontal = 0

[node name="EmoteTouch" parent="HUD" instance=ExtResource("5_lh883")]
layout_mode = 1
metadata/_edit_lock_ = true

[node name="HungerShow" parent="HUD" instance=ExtResource("13_db3us")]
layout_mode = 1
offset_left = 888.0
offset_top = 102.0
offset_right = -33.0
offset_bottom = 150.0

[node name="MobileHudParent" type="Control" parent="HUD"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
metadata/_edit_lock_ = true

[node name="MobileButton" parent="HUD" instance=ExtResource("6_y3tdy")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -103.0
offset_top = 152.0
offset_right = -32.0
offset_bottom = 223.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(35.5, 35.5)

[node name="BGImage" type="TextureRect" parent="HUD/MobileButton"]
custom_minimum_size = Vector2(60, 60)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -30.0
offset_top = -30.0
offset_right = 30.0
offset_bottom = 30.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("12_hbof3")
expand_mode = 1
stretch_mode = 5

[node name="image" type="TextureRect" parent="HUD/MobileButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -19.5
offset_top = -19.5
offset_right = 20.5
offset_bottom = 21.5
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("10_waqvo")
expand_mode = 1
stretch_mode = 5

[node name="Ringing" type="Panel" parent="HUD/MobileButton"]
visible = false
custom_minimum_size = Vector2(60, 60)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_8ikte")

[node name="Texture" type="TextureRect" parent="HUD/MobileButton/Ringing"]
custom_minimum_size = Vector2(60, 60)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -30.0
offset_top = -30.0
offset_right = 30.0
offset_bottom = 30.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("12_hbof3")
expand_mode = 1
stretch_mode = 5

[node name="Texture2" type="TextureRect" parent="HUD/MobileButton/Ringing"]
custom_minimum_size = Vector2(60, 60)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -30.0
offset_top = -30.0
offset_right = 30.0
offset_bottom = 30.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("12_tkg5p")
expand_mode = 1
stretch_mode = 5

[node name="MiniMapButton" parent="HUD" instance=ExtResource("6_y3tdy")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -194.0
offset_top = 18.0
offset_right = -124.0
offset_bottom = 88.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(35, 35)

[node name="TextureRect" type="TextureRect" parent="HUD/MiniMapButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.87, 0.87)
pivot_offset = Vector2(35, 35)
mouse_filter = 2
texture = ExtResource("13_gc0ya")
expand_mode = 1
stretch_mode = 4

[node name="FreeRideChat" parent="HUD" instance=ExtResource("14_5ir2c")]
layout_mode = 1
metadata/_edit_lock_ = true

[node name="JobPanel" parent="HUD" instance=ExtResource("12_11jyk")]
layout_mode = 1
metadata/_edit_lock_ = true

[node name="ExitButton" parent="HUD" instance=ExtResource("6_y3tdy")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -101.0
offset_top = 19.0
offset_right = -31.0
offset_bottom = 89.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(35, 35)

[node name="TextureRect" type="TextureRect" parent="HUD/ExitButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.87, 0.87)
pivot_offset = Vector2(35, 35)
mouse_filter = 2
texture = ExtResource("16_3pydt")
expand_mode = 1
stretch_mode = 4

[node name="CoinAdderUI" parent="HUD" instance=ExtResource("16_vnpdg")]
visible = false
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -107.0
offset_top = 24.0
offset_right = 43.0
offset_bottom = 74.0
grow_vertical = 1

[node name="Connecting" type="Panel" parent="HUD"]
visible = false
z_index = 3
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_wpvqg")

[node name="LoadingElement2" parent="HUD/Connecting" instance=ExtResource("10_ied73")]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -4.0
offset_top = 44.0
offset_right = 36.0
offset_bottom = 84.0
rotation = 4.15823

[node name="WaitingLabel" type="Label" parent="HUD/Connecting"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -163.5
offset_top = -29.0
offset_right = 163.5
offset_bottom = 29.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 30
text = "CONNECTING_TO_SERVER"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TextHint" type="Label" parent="HUD/Connecting"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -72.0
offset_bottom = -21.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 25
horizontal_alignment = 1
vertical_alignment = 1

[node name="DebugControl" type="Control" parent="HUD"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 491.0
offset_right = -309.0
offset_bottom = -638.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 1

[node name="Panel" type="Panel" parent="HUD/DebugControl"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_mtps7")

[node name="HBoxContainer" type="HBoxContainer" parent="HUD/DebugControl"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20

[node name="Debuggm1" type="CheckButton" parent="HUD/DebugControl/HBoxContainer"]
visible = false
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 0
button_pressed = true

[node name="Label" type="Label" parent="HUD/DebugControl/HBoxContainer/Debuggm1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 32.0
offset_top = 52.0
offset_right = 32.0
grow_horizontal = 2
grow_vertical = 2
text = "Occlusion"
horizontal_alignment = 1

[node name="Debuggm2" type="CheckButton" parent="HUD/DebugControl/HBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
button_pressed = true

[node name="Label" type="Label" parent="HUD/DebugControl/HBoxContainer/Debuggm2"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 32.0
offset_top = 52.0
offset_right = 32.0
grow_horizontal = 2
grow_vertical = 2
text = "P1"
horizontal_alignment = 1

[node name="Debugflowerbastan" type="CheckButton" parent="HUD/DebugControl/HBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
button_pressed = true

[node name="Label" type="Label" parent="HUD/DebugControl/HBoxContainer/Debugflowerbastan"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 29.0
offset_top = 52.0
offset_right = 35.0
grow_horizontal = 2
grow_vertical = 2
text = "P2"
horizontal_alignment = 1

[node name="Debugbastan" type="CheckButton" parent="HUD/DebugControl/HBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
button_pressed = true

[node name="Label" type="Label" parent="HUD/DebugControl/HBoxContainer/Debugbastan"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 29.0
offset_top = 52.0
offset_right = 29.0
grow_horizontal = 2
grow_vertical = 2
text = "P3"
horizontal_alignment = 1

[node name="Debugmosque" type="CheckButton" parent="HUD/DebugControl/HBoxContainer"]
visible = false
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
button_pressed = true

[node name="Label" type="Label" parent="HUD/DebugControl/HBoxContainer/Debugmosque"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 27.0
offset_top = 52.0
offset_right = 27.0
grow_horizontal = 2
grow_vertical = 2
text = "Mosque"
horizontal_alignment = 1

[node name="Debugflowerpersian" type="CheckButton" parent="HUD/DebugControl/HBoxContainer"]
visible = false
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
button_pressed = true

[node name="Label" type="Label" parent="HUD/DebugControl/HBoxContainer/Debugflowerpersian"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 23.0
offset_top = 52.0
offset_right = 33.0
grow_horizontal = 2
grow_vertical = 2
text = "FlowerPersian"
horizontal_alignment = 1

[node name="DebugDisco" type="CheckButton" parent="HUD/DebugControl/HBoxContainer"]
visible = false
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
button_pressed = true

[node name="Label" type="Label" parent="HUD/DebugControl/HBoxContainer/DebugDisco"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 29.0
offset_top = 52.0
offset_right = 29.0
grow_horizontal = 2
grow_vertical = 2
text = "DiscoLight"
horizontal_alignment = 1

[node name="Debugwater" type="CheckButton" parent="HUD/DebugControl/HBoxContainer"]
visible = false
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
button_pressed = true

[node name="Label" type="Label" parent="HUD/DebugControl/HBoxContainer/Debugwater"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 32.0
offset_top = 52.0
offset_right = 32.0
grow_horizontal = 2
grow_vertical = 2
text = "Water
"
horizontal_alignment = 1

[node name="LoadingMap" type="Panel" parent="HUD"]
visible = false
z_index = 10
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_bkext")

[node name="Label" type="Label" parent="HUD/LoadingMap"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -148.0
offset_top = -29.0
offset_right = 148.0
offset_bottom = 29.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 30
text = "LOADING_MAP"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingElement" parent="HUD/LoadingMap" instance=ExtResource("10_ied73")]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = 52.0
offset_right = 20.0
offset_bottom = 92.0
rotation = 4.15823

[node name="ProgressBar" type="TextureProgressBar" parent="HUD/LoadingMap"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -425.0
offset_top = -190.0
offset_right = 428.0
offset_bottom = -145.0
grow_horizontal = 2
grow_vertical = 0
step = 0.1
nine_patch_stretch = true
stretch_margin_left = 10
stretch_margin_top = 10
stretch_margin_right = 10
stretch_margin_bottom = 10
texture_under = ExtResource("12_w5avr")
texture_progress = ExtResource("13_8pmws")

[node name="ProgressLabel" type="Label" parent="HUD/LoadingMap/ProgressBar"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -149.5
offset_top = -48.0
offset_right = 146.5
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 30
text = "0%"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TextHint" type="Label" parent="HUD/LoadingMap"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -72.0
offset_bottom = -21.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 25
horizontal_alignment = 1
vertical_alignment = 1

[node name="ShaderLabel" type="Label" parent="HUD/LoadingMap"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -163.5
offset_top = -317.0
offset_right = 163.5
offset_bottom = -259.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_color = Color(0.160784, 0.196078, 0.227451, 1)
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 30
text = "SHADER_LOADING"
horizontal_alignment = 1

[node name="ExitPopup" parent="HUD" instance=ExtResource("12_wkxoy")]
visible = false
z_index = 20
layout_mode = 1

[node name="MiniMap" parent="HUD" instance=ExtResource("17_11yvo")]
process_mode = 4
visible = false
z_index = 1
layout_mode = 1

[node name="CameraController" type="Node3D" parent="."]
script = ExtResource("14_vsc10")

[node name="SpringArm3D" type="SpringArm3D" parent="CameraController"]
transform = Transform3D(-1, 5.53401e-08, -1.40489e-07, 0, 0.930418, 0.366501, 1.50996e-07, 0.366501, -0.930418, 0, 2.706, 0)
collision_mask = 146
spring_length = 4.0
margin = 0.2

[node name="Camera3D" type="Camera3D" parent="CameraController/SpringArm3D"]
transform = Transform3D(1, 5.38973e-08, -1.66275e-07, -6.56449e-08, 0.997442, -0.0714804, 1.61997e-07, 0.0714804, 0.997442, 7.87108e-12, -0.454067, -0.178958)
current = true
fov = 90.0

[node name="LookAt" type="Node3D" parent="CameraController"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 4)

[node name="MeshInstance3D" type="MeshInstance3D" parent="CameraController/LookAt"]
visible = false
mesh = SubResource("SphereMesh_fry5p")

[node name="VehicleCamera3D" type="Camera3D" parent="."]
transform = Transform3D(-1, 4.33694e-08, -1.44085e-07, -1.70514e-09, 0.954235, 0.299057, 1.50461e-07, 0.299057, -0.954235, 5.64704e-12, 2.21794, 8.97348e-05)
script = ExtResource("29_qyomj")
follow_distance = 6.0
follow_height = 5.0

[node name="Pointer" type="Node3D" parent="." groups=["pointer"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 3)

[node name="ExitingPanel" type="Panel" parent="."]
visible = false
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_bkext")

[node name="Label" type="Label" parent="ExitingPanel"]
layout_mode = 1
anchors_preset = 14
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_top = -25.5
offset_bottom = 25.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 30
text = "EXITING"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Transition" type="TextureRect" parent="."]
visible = false
material = SubResource("ShaderMaterial_eh28b")
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 0
texture = SubResource("CanvasTexture_lh488")

[node name="FreeRideMusicSelector" type="Node" parent="."]
script = ExtResource("20_xffcm")

[node name="MapParent" type="Node3D" parent="."]

[node name="ClientReconnector" type="Control" parent="."]
visible = false
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("23_jcnc1")

[node name="Panel" type="Panel" parent="ClientReconnector"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = ExtResource("25_wttxw")

[node name="Label" type="Label" parent="ClientReconnector/Panel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -86.0
offset_top = 8.0
offset_right = 86.0
offset_bottom = 34.0
grow_horizontal = 2
theme_override_fonts/font = ExtResource("3_ghefi")
text = "RECONNECTING_TITLE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LabelText" type="Label" parent="ClientReconnector/Panel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -139.5
offset_top = -137.5
offset_right = 139.5
offset_bottom = 137.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 7
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 30
text = "RECONNECTING_TEXT"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="BackToMainMenu" parent="ClientReconnector/Panel" instance=ExtResource("6_y3tdy")]
custom_minimum_size = Vector2(150, 50)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
offset_left = -82.5
offset_top = -87.0
offset_right = 82.5
offset_bottom = -16.0
grow_vertical = 0
pivot_offset = Vector2(82.5, 35.5)

[node name="TextureRect" type="TextureRect" parent="ClientReconnector/Panel/BackToMainMenu"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("26_ymo16")
expand_mode = 1
metadata/_edit_lock_ = true

[node name="text" type="Label" parent="ClientReconnector/Panel/BackToMainMenu"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("3_ghefi")
theme_override_font_sizes/font_size = 25
text = "EXIT"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingElement" parent="ClientReconnector/Panel" instance=ExtResource("10_ied73")]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -10.0
offset_top = 159.0
offset_right = 5.0
offset_bottom = 174.0
pivot_offset = Vector2(8, 8)

[connection signal="tree_exiting" from="." to="." method="_on_tree_exiting"]
[connection signal="timeout" from="DayNightTimeCounter" to="." method="handle_world_enviroment"]
[connection signal="pressed" from="HUD/MobileButton" to="." method="_on_mobile_button_pressed"]
[connection signal="pressed" from="HUD/MiniMapButton" to="." method="_on_mini_map_button_pressed"]
[connection signal="pressed" from="HUD/ExitButton" to="." method="_on_exit_button_pressed"]
[connection signal="toggled" from="HUD/DebugControl/HBoxContainer/Debuggm1" to="." method="_on_debuggm_1_toggled"]
[connection signal="toggled" from="HUD/DebugControl/HBoxContainer/Debuggm2" to="." method="_on_debuggm_2_toggled"]
[connection signal="toggled" from="HUD/DebugControl/HBoxContainer/Debugflowerbastan" to="." method="_on_debugflowerbastan_toggled"]
[connection signal="toggled" from="HUD/DebugControl/HBoxContainer/Debugbastan" to="." method="_on_debugbastan_toggled"]
[connection signal="toggled" from="HUD/DebugControl/HBoxContainer/Debugmosque" to="." method="_on_debugmosque_toggled"]
[connection signal="toggled" from="HUD/DebugControl/HBoxContainer/Debugflowerpersian" to="." method="_on_debugflowerpersian_toggled"]
[connection signal="toggled" from="HUD/DebugControl/HBoxContainer/DebugDisco" to="." method="_on_debug_disco_toggled"]
[connection signal="toggled" from="HUD/DebugControl/HBoxContainer/Debugwater" to="." method="_on_debugwater_toggled"]
[connection signal="pressed" from="ClientReconnector/Panel/BackToMainMenu" to="ClientReconnector" method="_on_back_to_main_menu_pressed"]
