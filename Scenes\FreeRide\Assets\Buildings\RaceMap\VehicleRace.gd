extends Node3D
class_name VehicleRace

@export var id = ""

@onready var state_machine: VehicleStateMachine = $StateMachine
@onready var start_hud: ExitableControl = $StartHUD
@onready var player2_coin: Label = $StartHUD/Panel/VBoxContainer/Player2/HBoxContainer/coin
@onready var player3_coin: Label = $StartHUD/Panel/VBoxContainer/Player3/HBoxContainer/coin
@onready var player4_coin: Label = $StartHUD/Panel/VBoxContainer/Player4/HBoxContainer/coin
@onready var player2_label: Label = $StartHUD/Panel/VBoxContainer/Player2/Label
@onready var player3_label: Label = $StartHUD/Panel/VBoxContainer/Player3/Label
@onready var player4_label: Label = $StartHUD/Panel/VBoxContainer/Player4/Label
@onready var start_button: PurchaseButton = $StartHUD/Panel/StartButton
@onready var exit_confirm_popup: Panel = $ExitConfirmPopup
@onready var checkpoints: Node3D = $Checkpoints
@onready var result_hud: Panel = $ResultHUD


var minigame_backend_id
var my_vehicle_at_start

func _ready() -> void:
	result_hud.visible = false
	exit_confirm_popup.visible  = false
	_on_start_hud_touched()


var should_reset = false
func _physics_process(_delta: float) -> void:
	if should_reset:
		reset_vehicle()


func _on_area_3d_body_entered(p: Node3D) -> void:
	if Constants.is_server or (p is Character == false):
		return
	
	var player: Character = p
	
	if player.is_me():
		if VehicleManager.ami_in_vehicle() == false:
			_on_start_hud_touched()
			Constants.show_toast(tr("VEHICLE_RACE_ERROR"))
			return
		if state_machine.client_im_in:
			return
	
		start_hud.visible = true
		start_hud.process_mode = Node.PROCESS_MODE_INHERIT
		player2_label.text = tr("MENCH_PRIZE") % [2]
		player3_label.text = tr("MENCH_PRIZE") % [3]
		player4_label.text = tr("MENCH_PRIZE") % [4]
		player2_coin.text = str(Selector.vehicle_race_2player_prize * 1)
		player3_coin.text = str(Selector.vehicle_race_3player_prize * 1)
		player4_coin.text = str(Selector.vehicle_race_4player_prize * 1)
		start_button.set_coins(Selector.vehicle_race_price * 1)


func _on_area_3d_body_exited(p: Node3D) -> void:
	if Constants.is_server or (p is Character == false):
		return
	
	var player: Character = p
	if player.is_me():
		_on_start_hud_touched()


func _on_start_hud_touched() -> void:
	start_hud.visible = false
	start_hud.process_mode = Node.PROCESS_MODE_DISABLED


func _on_start_button_pressed() -> void:
	_on_start_hud_touched()
	
	if Selector.my_coin < Selector.vehicle_race_price:
		Constants.show_toast(tr("NOT_ENOUGH_COIN"))
		return
	
	state_machine.on_player_enter.rpc_id(1, Selector.my_smart)


func _on_exit_button_pressed() -> void:
	_on_start_hud_touched()


func _on_race_exit_button_pressed() -> void:
	exit_confirm_popup.visible = true


func _on_exit_confirm_pressed() -> void:
	state_machine.on_player_exit.rpc_id(1)
	exit_confirm_popup.visible = false


func _on_exit_cancel_pressed() -> void:
	exit_confirm_popup.visible = false


func _on_reset_button_pressed() -> void:
	if not is_instance_valid(my_vehicle_at_start):
		return
	
	should_reset = true


func reset_vehicle():
	should_reset = false
	if state_machine.client_im_in:
		for checkpoint:RaceCheckpoint in checkpoints.get_children():
			if checkpoint.id == state_machine.client_checkpoint:
				my_vehicle_at_start.global_position = checkpoint.global_position
				my_vehicle_at_start.global_rotation = checkpoint.global_rotation
				my_vehicle_at_start.global_rotation.y += deg_to_rad(checkpoint.y_adder)
				my_vehicle_at_start.freeze(0.1)
				if VehicleManager.ami_in_vehicle():
					Constants.client.my_player_scene.global_position = checkpoint.global_position
				var freeride_scene = Constants.server.game_scene as FreeRide
				freeride_scene.reset_vehicle_camera()
				return


func _on_results_exit_confirm_pressed() -> void:
	result_hud.visible = false


func on_exit_freeride():
	state_machine.i_exited()
	result_hud.visible = false


func on_reconnect():
	state_machine.i_exited()
	result_hud.visible = false
