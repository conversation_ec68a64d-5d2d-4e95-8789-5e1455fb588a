#!/bin/bash
#Used in Hamravesh docker
#Server Production
set -eux
idx=$(hostname -s | rev | cut -d'-' -f 1 | rev)
export PUBLIC_PORT=$(($BASE_PORT+idx))
#export PUBLIC_PORT=9000
SERVER=$PUBLIC_IP
PORT=$PUBLIC_PORT
RUNNER=/home/<USER>/server
MODE=$GAME_MODE
#RUNNER="godot"


$RUNNER --headless -- syncv2=$SYNCV2 server=1 ip=$SERVER port=9000 register_port=$PORT token="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoyLCJlbWFpbCI6IiIsInVzZXJuYW1lIjoiZ2FtZXNlcnZlciIsImV4cCI6MTg0NzIwMDUzNH0.hdNG7OUn06EafpMKv0s866osMudDyiczbplMxXPMW18" backend="https://api.sizakgames.ir" mode=$MODE
