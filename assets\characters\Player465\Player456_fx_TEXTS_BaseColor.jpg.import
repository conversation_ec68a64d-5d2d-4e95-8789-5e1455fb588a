[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://bm7je1ff846hh"
path.s3tc="res://.godot/imported/Player456_fx_TEXTS_BaseColor.jpg-f802fbbf4f0b6be206b82831a754dd3f.s3tc.ctex"
path.etc2="res://.godot/imported/Player456_fx_TEXTS_BaseColor.jpg-f802fbbf4f0b6be206b82831a754dd3f.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "411bb515c6ded975dcbb49dc4fcb9653"
}

[deps]

source_file="res://assets/characters/Player465/Player456_fx_TEXTS_BaseColor.jpg"
dest_files=["res://.godot/imported/Player456_fx_TEXTS_BaseColor.jpg-f802fbbf4f0b6be206b82831a754dd3f.s3tc.ctex", "res://.godot/imported/Player456_fx_TEXTS_BaseColor.jpg-f802fbbf4f0b6be206b82831a754dd3f.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
