[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://ut011ugavxh3"
path.s3tc="res://.godot/imported/RaceMapV1_TiresLP_lambert31_Normal.jpg-d1dd6e4d467e0572cf0f56775f62128d.s3tc.ctex"
path.etc2="res://.godot/imported/RaceMapV1_TiresLP_lambert31_Normal.jpg-d1dd6e4d467e0572cf0f56775f62128d.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "f7dabe586a737b69f88357749eff05bf"
}

[deps]

source_file="res://Scenes/FreeRide/Assets/Buildings/RaceMap/RaceMapV1_TiresLP_lambert31_Normal.jpg"
dest_files=["res://.godot/imported/RaceMapV1_TiresLP_lambert31_Normal.jpg-d1dd6e4d467e0572cf0f56775f62128d.s3tc.ctex", "res://.godot/imported/RaceMapV1_TiresLP_lambert31_Normal.jpg-d1dd6e4d467e0572cf0f56775f62128d.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://Scenes/FreeRide/Assets/Buildings/RaceMap/RaceMapV1_TiresLP_lambert31_Normal.jpg"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
