extends Panel

signal pressed(text)

@onready var time_label: Label = $TimeLabel
@onready var name_label: Label = $NameLabel
@onready var crime_label: Label = $CrimeLabel
@onready var ban_time: Label = $BanTime
@onready var jail_time: Label = $JailTime



var text

func set_data(data):
	time_label.text = Constants.pretify_time(data["created_time"])
	name_label.text = data["guilty_handle"]
	crime_label.text = data["crime"]
	ban_time.text = ban_time_convert(data["ban_time"])
	jail_time.text = ban_time_convert(data["jail_time"])
	text = data["text"]


func ban_time_convert(time):
	if time < 60:
		return str(time) + " ثانیه"
	
	return str(time / 60) + " دقیقه"


func _on_button_pressed() -> void:
	pressed.emit(text)
