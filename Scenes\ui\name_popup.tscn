[gd_scene load_steps=16 format=3 uid="uid://bjbx34eycyyqr"]

[ext_resource type="PackedScene" uid="uid://bt5xl1rexbt84" path="res://Scenes/ui/base_popup.tscn" id="1_n7pfh"]
[ext_resource type="Script" path="res://Scenes/ui/name_popup.gd" id="2_5q5we"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="3_8kpmc"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="4_frtu3"]
[ext_resource type="StyleBox" uid="uid://bf5cgaao6unjy" path="res://Scenes/ui/LineEditBGStyle.tres" id="4_r5aby"]
[ext_resource type="Texture2D" uid="uid://ckdq8pyso0sc7" path="res://Scenes/ui/assets/lineedit.png" id="5_83ljt"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="6_21yoq"]
[ext_resource type="Texture2D" uid="uid://cdi8kfb3vhhfs" path="res://Scenes/ui/assets/button-back.png" id="6_gtd2o"]
[ext_resource type="PackedScene" uid="uid://dimgwrlco20uw" path="res://Scenes/ui/custom_button_grey.tscn" id="8_hsvqx"]
[ext_resource type="Texture2D" uid="uid://b010qsgl38ikk" path="res://Scenes/ui/assets/coin1.png" id="9_6emdw"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="9_sad6e"]
[ext_resource type="PackedScene" uid="uid://no80346usybq" path="res://Scenes/ui/FullScreenLoading.tscn" id="11_qxfih"]
[ext_resource type="Texture2D" uid="uid://cw0ou8f3ia7b4" path="res://Scenes/ui/assets/gray.png" id="12_fohmi"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_0nkdl"]
texture = ExtResource("3_8kpmc")

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_sgeuk"]
texture = ExtResource("5_83ljt")

[node name="NamePopup" instance=ExtResource("1_n7pfh")]
script = ExtResource("2_5q5we")

[node name="BGPanel" parent="." index="0"]
focus_mode = 1
theme_override_styles/panel = SubResource("StyleBoxTexture_0nkdl")

[node name="NinePatchRect" parent="." index="1"]
offset_left = 0.0
offset_top = 0.0
offset_right = 0.0
offset_bottom = 0.0
focus_mode = 1
texture = null

[node name="LineEdit" type="LineEdit" parent="NinePatchRect" index="1"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -184.0
offset_top = 83.0
offset_right = 179.0
offset_bottom = 171.0
grow_horizontal = 2
theme_override_colors/selection_color = Color(0, 0, 0, 1)
theme_override_colors/caret_color = Color(0, 0, 0, 1)
theme_override_colors/font_placeholder_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_colors/font_selected_color = Color(0.243137, 0.243137, 0.243137, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_constants/outline_size = 6
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = ExtResource("4_r5aby")
theme_override_styles/normal = SubResource("StyleBoxTexture_sgeuk")
alignment = 1
max_length = 12
select_all_on_focus = true

[node name="Label" parent="NinePatchRect" index="2"]
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -144.0
offset_top = 34.0
offset_right = 144.0
offset_bottom = 86.0
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 29
text = "ENTER_YOUR_NAME"

[node name="ExitButtonss" parent="NinePatchRect" index="3"]
visible = false

[node name="ExitButton" parent="." index="2" instance=ExtResource("6_21yoq")]
custom_minimum_size = Vector2(50, 50)
layout_mode = 1
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 41.0
offset_top = 20.0
offset_right = 123.0
offset_bottom = 93.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(41, 36.5)
metadata/_edit_lock_ = true

[node name="TextureRect" type="TextureRect" parent="ExitButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_gtd2o")
expand_mode = 1
stretch_mode = 4

[node name="LoadingElement" parent="." index="3" instance=ExtResource("9_sad6e")]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -20.0
offset_top = 208.0
offset_right = 20.0
offset_bottom = 248.0
grow_vertical = 1

[node name="AcceptButton" parent="." index="4" instance=ExtResource("8_hsvqx")]
custom_minimum_size = Vector2(200, 50)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -96.0
offset_top = 189.0
offset_right = 104.0
offset_bottom = 270.0
grow_vertical = 1
pivot_offset = Vector2(100, 40.5)

[node name="Label" type="Label" parent="AcceptButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 2.0
offset_right = 2.0
offset_bottom = -16.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 25
text = "ACCEPT"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Price" type="Control" parent="AcceptButton" index="2"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 37.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="Label" type="Label" parent="AcceptButton/Price" index="0"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 6.0
offset_right = -93.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 25
text = "50"
horizontal_alignment = 2
vertical_alignment = 1

[node name="TextureRect" type="TextureRect" parent="AcceptButton/Price" index="1"]
layout_mode = 0
offset_left = 101.0
offset_right = 141.0
offset_bottom = 40.0
mouse_filter = 2
texture = ExtResource("9_6emdw")
expand_mode = 1
stretch_mode = 4

[node name="LinkButton" parent="." index="5" instance=ExtResource("8_hsvqx")]
custom_minimum_size = Vector2(200, 50)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -248.0
offset_top = 26.0
offset_right = -48.0
offset_bottom = 95.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(100, 34.5)

[node name="Label" type="Label" parent="LinkButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 2.0
offset_right = 2.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 25
text = "LINK"
horizontal_alignment = 1
vertical_alignment = 1
metadata/_edit_lock_ = true

[node name="LinkEmail" type="Control" parent="." index="6"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 1
mouse_filter = 2
metadata/_edit_lock_ = true

[node name="VBoxContainer" type="VBoxContainer" parent="LinkEmail" index="0"]
custom_minimum_size = Vector2(403, 0)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -403.0
offset_top = 48.0
offset_bottom = 88.0
grow_horizontal = 0
theme_override_constants/separation = 10

[node name="Label" type="Label" parent="LinkEmail/VBoxContainer" index="0"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 25
text = "LINK_EMAIL"
horizontal_alignment = 1
vertical_alignment = 1

[node name="email" type="Label" parent="LinkEmail/VBoxContainer" index="1"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 25
text = "LINK_EMAIL"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ChangeAccountButton" parent="LinkEmail/VBoxContainer" index="2" instance=ExtResource("8_hsvqx")]
custom_minimum_size = Vector2(160, 69)
layout_mode = 2
size_flags_horizontal = 4

[node name="Label" type="Label" parent="LinkEmail/VBoxContainer/ChangeAccountButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 2.0
offset_right = 2.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 20
text = "CHANGE_ACCOUNT"
horizontal_alignment = 1
vertical_alignment = 1
metadata/_edit_lock_ = true

[node name="RemoveEmail" parent="LinkEmail/VBoxContainer" index="3" instance=ExtResource("8_hsvqx")]
custom_minimum_size = Vector2(160, 69)
layout_mode = 2
size_flags_horizontal = 4

[node name="Label" type="Label" parent="LinkEmail/VBoxContainer/RemoveEmail" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 2.0
offset_right = 2.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 20
text = "REMOVE_EMAIL"
horizontal_alignment = 1
vertical_alignment = 1
metadata/_edit_lock_ = true

[node name="RemoveEmailHTTP" type="HTTPRequest" parent="LinkEmail" index="1"]

[node name="LinkStep1" type="Control" parent="." index="7"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 1

[node name="BGPanel" type="Panel" parent="LinkStep1" index="0"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -13.0
offset_right = 22.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 1
theme_override_styles/panel = SubResource("StyleBoxTexture_0nkdl")

[node name="BackButtonStep1" parent="LinkStep1" index="1" instance=ExtResource("6_21yoq")]
custom_minimum_size = Vector2(50, 50)
layout_mode = 1
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 41.0
offset_top = 20.0
offset_right = 123.0
offset_bottom = 93.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(53, 49)

[node name="TextureRect" type="TextureRect" parent="LinkStep1/BackButtonStep1" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_gtd2o")
expand_mode = 1
stretch_mode = 4

[node name="EmailLineEdit" type="LineEdit" parent="LinkStep1" index="2"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -184.0
offset_top = 83.0
offset_right = 179.0
offset_bottom = 171.0
grow_horizontal = 2
theme_override_colors/selection_color = Color(0, 0, 0, 1)
theme_override_colors/caret_color = Color(0, 0, 0, 1)
theme_override_colors/font_placeholder_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_colors/font_selected_color = Color(0.243137, 0.243137, 0.243137, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_constants/outline_size = 6
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = ExtResource("4_r5aby")
theme_override_styles/normal = SubResource("StyleBoxTexture_sgeuk")
alignment = 1
select_all_on_focus = true

[node name="ErrorLabelStep1" type="Label" parent="LinkStep1/EmailLineEdit" index="0"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = 8.0
offset_top = -26.0
offset_right = 296.0
offset_bottom = 26.0
grow_horizontal = 0
grow_vertical = 2
theme_override_colors/font_color = Color(0.972549, 0.027451, 0, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 29
text = "ERROR_INVALID_EMAIL"
vertical_alignment = 1

[node name="Label" type="Label" parent="LinkStep1" index="3"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -144.0
offset_top = 34.0
offset_right = 144.0
offset_bottom = 86.0
grow_horizontal = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 29
text = "ENTER_YOUR_EMAIL"
horizontal_alignment = 1
vertical_alignment = 1

[node name="NextStepStep1Button" parent="LinkStep1" index="4" instance=ExtResource("8_hsvqx")]
custom_minimum_size = Vector2(200, 50)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -96.0
offset_top = 189.0
offset_right = 104.0
offset_bottom = 258.0
grow_vertical = 1
pivot_offset = Vector2(100, 34.5)

[node name="Label" type="Label" parent="LinkStep1/NextStepStep1Button" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 2.0
offset_right = 2.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 25
text = "ACCEPT"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingElement" parent="LinkStep1" index="5" instance=ExtResource("9_sad6e")]
visible = false
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -19.9999
offset_top = 201.0
offset_right = 20.0001
offset_bottom = 241.0
grow_vertical = 1

[node name="LinkStep2" type="Control" parent="." index="8"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 1

[node name="BGPanel" type="Panel" parent="LinkStep2" index="0"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -13.0
offset_right = 22.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 1
theme_override_styles/panel = SubResource("StyleBoxTexture_0nkdl")

[node name="BackButtonStep2" parent="LinkStep2" index="1" instance=ExtResource("6_21yoq")]
custom_minimum_size = Vector2(50, 50)
layout_mode = 1
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 41.0
offset_top = 20.0
offset_right = 123.0
offset_bottom = 93.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(53, 49)

[node name="TextureRect" type="TextureRect" parent="LinkStep2/BackButtonStep2" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_gtd2o")
expand_mode = 1
stretch_mode = 4

[node name="CodeLineEdit" type="LineEdit" parent="LinkStep2" index="2"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -184.0
offset_top = 83.0
offset_right = 179.0
offset_bottom = 171.0
grow_horizontal = 2
theme_override_colors/selection_color = Color(0, 0, 0, 1)
theme_override_colors/caret_color = Color(0, 0, 0, 1)
theme_override_colors/font_placeholder_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_colors/font_selected_color = Color(0.243137, 0.243137, 0.243137, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_constants/outline_size = 6
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = ExtResource("4_r5aby")
theme_override_styles/normal = SubResource("StyleBoxTexture_sgeuk")
alignment = 1
select_all_on_focus = true

[node name="ErrorLabelStep2" type="Label" parent="LinkStep2/CodeLineEdit" index="0"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = 8.0
offset_top = -26.0
offset_right = 296.0
offset_bottom = 26.0
grow_horizontal = 0
grow_vertical = 2
theme_override_colors/font_color = Color(0.972549, 0.027451, 0, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 29
text = "WRONG_CODE"
vertical_alignment = 1

[node name="Label" type="Label" parent="LinkStep2" index="3"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -144.0
offset_top = 34.0
offset_right = 144.0
offset_bottom = 86.0
grow_horizontal = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 29
text = "ENTER_YOUR_CODE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="NextStepStep2Button" parent="LinkStep2" index="4" instance=ExtResource("8_hsvqx")]
custom_minimum_size = Vector2(200, 50)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -96.0
offset_top = 189.0
offset_right = 104.0
offset_bottom = 258.0
grow_vertical = 1
pivot_offset = Vector2(100, 34.5)

[node name="Label" type="Label" parent="LinkStep2/NextStepStep2Button" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 2.0
offset_right = 2.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 25
text = "ACCEPT"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingElement2" parent="LinkStep2" index="5" instance=ExtResource("9_sad6e")]
visible = false
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -19.9999
offset_top = 201.0
offset_right = 20.0001
offset_bottom = 241.0
grow_vertical = 1

[node name="LinkStep3" type="Control" parent="." index="9"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 1

[node name="BGPanel" type="Panel" parent="LinkStep3" index="0"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -13.0
offset_right = 22.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_0nkdl")

[node name="Label" type="Label" parent="LinkStep3" index="1"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -144.0
offset_top = 34.0
offset_right = 144.0
offset_bottom = 86.0
grow_horizontal = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 29
text = "LINK_SUCCESS"
horizontal_alignment = 1
vertical_alignment = 1

[node name="FinishButton" parent="LinkStep3" index="2" instance=ExtResource("8_hsvqx")]
custom_minimum_size = Vector2(200, 50)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -96.0
offset_top = 109.0
offset_right = 104.0
offset_bottom = 178.0
grow_vertical = 1
pivot_offset = Vector2(100, 34.5)

[node name="Label" type="Label" parent="LinkStep3/FinishButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 2.0
offset_right = 2.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 25
text = "ACCEPT"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingElement2" parent="LinkStep3" index="3" instance=ExtResource("9_sad6e")]
visible = false
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -19.9999
offset_top = 201.0
offset_right = 20.0001
offset_bottom = 241.0
grow_vertical = 1

[node name="RequestCodeHTTPRequest" type="HTTPRequest" parent="." index="10"]

[node name="VerifyCodeHTTPRequest" type="HTTPRequest" parent="." index="11"]

[node name="Loading" parent="." index="12" instance=ExtResource("11_qxfih")]
visible = false
layout_mode = 1

[node name="RemoveRequestCodeHTTP" type="HTTPRequest" parent="." index="13"]

[node name="RemoveVerifyCodeHTTP" type="HTTPRequest" parent="." index="14"]

[node name="RemoveEmailStep1" type="Control" parent="." index="15"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="BGPanel" type="Panel" parent="RemoveEmailStep1" index="0"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_0nkdl")

[node name="Label" type="Label" parent="RemoveEmailStep1" index="1"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -200.0
offset_top = 110.0
offset_right = 200.0
offset_bottom = 160.0
grow_horizontal = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 29
text = "CONFIRM_EMAIL_REMOVAL"
horizontal_alignment = 1
vertical_alignment = 1

[node name="EmailLineEdit" type="LineEdit" parent="RemoveEmailStep1" index="2"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -200.0
offset_top = 180.0
offset_right = 200.0
offset_bottom = 267.0
grow_horizontal = 2
theme_override_colors/font_uneditable_color = Color(0, 0, 0, 1)
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 20
theme_override_styles/focus = ExtResource("4_r5aby")
theme_override_styles/read_only = ExtResource("4_r5aby")
theme_override_styles/normal = ExtResource("4_r5aby")
alignment = 1
editable = false

[node name="ErrorLabel" type="Label" parent="RemoveEmailStep1/EmailLineEdit" index="0"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -100.0
offset_top = 15.0
offset_right = 100.0
offset_bottom = 45.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_color = Color(1, 0.2, 0.2, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 16
text = "ERROR_SENDING_CODE"
horizontal_alignment = 1

[node name="NextButton" parent="RemoveEmailStep1" index="3" instance=ExtResource("6_21yoq")]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -80.0
offset_top = 324.0
offset_right = 80.0
offset_bottom = 395.0
grow_vertical = 1
pivot_offset = Vector2(80, 35.5)

[node name="BG" type="NinePatchRect" parent="RemoveEmailStep1/NextButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("12_fohmi")
patch_margin_left = 19
patch_margin_top = 24
patch_margin_right = 42
patch_margin_bottom = 26

[node name="Label" type="Label" parent="RemoveEmailStep1/NextButton" index="2"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 20
text = "SEND_CODE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BackButton" parent="RemoveEmailStep1" index="4" instance=ExtResource("6_21yoq")]
layout_mode = 1
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 40.0
offset_top = 24.0
offset_right = 118.0
offset_bottom = 95.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(39, 35.5)

[node name="TextureRect" type="TextureRect" parent="RemoveEmailStep1/BackButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_gtd2o")
expand_mode = 1
stretch_mode = 4

[node name="LoadingElement" parent="RemoveEmailStep1" index="5" instance=ExtResource("9_sad6e")]
visible = false
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -24.9999
offset_top = 332.0
offset_right = 25.0001
offset_bottom = 382.0
grow_vertical = 1
pivot_offset = Vector2(25, 25)

[node name="RemoveEmailStep2" type="Control" parent="." index="16"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="BGPanel" type="Panel" parent="RemoveEmailStep2" index="0"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_0nkdl")

[node name="Label" type="Label" parent="RemoveEmailStep2" index="1"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -250.0
offset_top = 150.0
offset_right = 250.0
offset_bottom = 200.0
grow_horizontal = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 25
text = "ENTER_REMOVE_CODE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CodeLineEdit" type="LineEdit" parent="RemoveEmailStep2" index="2"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -150.0
offset_top = 220.0
offset_right = 150.0
offset_bottom = 293.0
grow_horizontal = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 20
theme_override_styles/focus = ExtResource("4_r5aby")
theme_override_styles/normal = SubResource("StyleBoxTexture_sgeuk")
placeholder_text = "Enter verification code"
alignment = 1
max_length = 6

[node name="ErrorLabel" type="Label" parent="RemoveEmailStep2/CodeLineEdit" index="0"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -100.0
offset_top = 10.0
offset_right = 100.0
offset_bottom = 40.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_color = Color(1, 0.2, 0.2, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 16
text = "INVALID_CODE"
horizontal_alignment = 1

[node name="NextButton" parent="RemoveEmailStep2" index="3" instance=ExtResource("6_21yoq")]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -80.0
offset_top = 340.0
offset_right = 80.0
offset_bottom = 411.0
grow_vertical = 1
pivot_offset = Vector2(80, 35.5)

[node name="BG" type="NinePatchRect" parent="RemoveEmailStep2/NextButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("12_fohmi")
patch_margin_left = 19
patch_margin_top = 24
patch_margin_right = 42
patch_margin_bottom = 26

[node name="Label" type="Label" parent="RemoveEmailStep2/NextButton" index="2"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_fonts/font = ExtResource("4_frtu3")
theme_override_font_sizes/font_size = 20
text = "REMOVE_EMAIL"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingElement" parent="RemoveEmailStep2" index="4" instance=ExtResource("9_sad6e")]
visible = false
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
anchor_bottom = 0.0
offset_left = -25.0
offset_top = 320.0
offset_right = 25.0
offset_bottom = 370.0
grow_vertical = 1

[node name="BackButton" parent="RemoveEmailStep2" index="5" instance=ExtResource("6_21yoq")]
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 40.0
offset_top = 24.0
offset_right = 118.0
offset_bottom = 95.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(39, 35.5)

[node name="TextureRect" type="TextureRect" parent="RemoveEmailStep2/BackButton" index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_gtd2o")
expand_mode = 1
stretch_mode = 4

[connection signal="hidden" from="." to="." method="_on_hidden"]
[connection signal="visibility_changed" from="." to="." method="_on_visibility_changed"]
[connection signal="pressed" from="ExitButton" to="." method="_on_exit_button_pressed"]
[connection signal="pressed" from="AcceptButton" to="." method="_on_accept_pressed"]
[connection signal="pressed" from="LinkButton" to="." method="_on_link_button_pressed"]
[connection signal="pressed" from="LinkEmail/VBoxContainer/ChangeAccountButton" to="." method="_on_change_account_button_pressed"]
[connection signal="pressed" from="LinkEmail/VBoxContainer/RemoveEmail" to="." method="_on_remove_email_pressed"]
[connection signal="request_completed" from="LinkEmail/RemoveEmailHTTP" to="." method="_on_remove_email_http_request_completed"]
[connection signal="pressed" from="LinkStep1/BackButtonStep1" to="." method="_on_back_button_step_1_pressed"]
[connection signal="pressed" from="LinkStep1/NextStepStep1Button" to="." method="_on_next_step_step_1_pressed"]
[connection signal="pressed" from="LinkStep2/BackButtonStep2" to="." method="_on_back_button_step_2_pressed"]
[connection signal="pressed" from="LinkStep2/NextStepStep2Button" to="." method="_on_next_step_step_2_button_pressed"]
[connection signal="pressed" from="LinkStep3/FinishButton" to="." method="_on_finish_button_pressed"]
[connection signal="pressed" from="RemoveEmailStep1/NextButton" to="." method="_on_remove_next_button_step1_pressed"]
[connection signal="pressed" from="RemoveEmailStep1/BackButton" to="." method="_on_remove_back_button_step1_pressed"]
[connection signal="pressed" from="RemoveEmailStep2/NextButton" to="." method="_on_remove_next_button_step2_pressed"]
[connection signal="pressed" from="RemoveEmailStep2/BackButton" to="." method="_on_remove_back_button_step2_pressed"]
