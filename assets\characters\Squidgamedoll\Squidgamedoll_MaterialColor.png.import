[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://d11b3luikl0oo"
path.s3tc="res://.godot/imported/Squidgamedoll_MaterialColor.png-e31144d58f49fbf7f784caa8e4a2b244.s3tc.ctex"
path.etc2="res://.godot/imported/Squidgamedoll_MaterialColor.png-e31144d58f49fbf7f784caa8e4a2b244.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "0d2bcb9c542dbe997033caa90282a3ed"
}

[deps]

source_file="res://assets/characters/Squidgamedoll/Squidgamedoll_MaterialColor.png"
dest_files=["res://.godot/imported/Squidgamedoll_MaterialColor.png-e31144d58f49fbf7f784caa8e4a2b244.s3tc.ctex", "res://.godot/imported/Squidgamedoll_MaterialColor.png-e31144d58f49fbf7f784caa8e4a2b244.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
