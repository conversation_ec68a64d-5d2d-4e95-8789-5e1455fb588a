extends StateMachineState

@onready var hud: Control = $HUD
@onready var count_down_label: Label = $HUD/CountDownLabel
@onready var counter_start_label: Label = $HUD/CounterStartLabel
@onready var inGameState: VehicleRaceIngameState = $"../InGame"
@onready var countdown_player: AudioStreamPlayer3D = $AudioStreamPlayer3D
@onready var vehicleRaceStateMachine: VehicleStateMachine = $".."
@onready var result_hud: Panel = $"../../ResultHUD"


var WAIT_TIME = 15.0#Seconds
#var WAIT_TIME = 2.0#Seconds
var time_counter = WAIT_TIME
var countdown = false


func _ready() -> void:
	countdown_player.stop()
	hud.visible = false
	hud.process_mode = Node.PROCESS_MODE_DISABLED
	result_hud.visible = false


func on_enter() -> void:
	result_hud.visible = false
	if Constants.is_server:
		vehicleRaceStateMachine.reset_game()
		stop_count_down()
	else:
		if vehicleRaceStateMachine.client_im_in:
			hud.visible = true
			hud.process_mode = Node.PROCESS_MODE_ALWAYS
		else:
			hud.visible = false
			hud.process_mode = Node.PROCESS_MODE_ALWAYS
		countdown_player.stop()


func on_process(delta: float) -> void:
	if Constants.is_server:
		if countdown:
			time_counter -= delta
			if time_counter <= 0:
				state_machine.current_state = inGameState


func on_physics_process(_delta: float) -> void:
	pass


func on_input(_event: InputEvent) -> void:
	pass


func on_exit() -> void:
	if Constants.is_server:
		stop_count_down()
	else:
		hud.visible = false
		hud.process_mode = Node.PROCESS_MODE_DISABLED


func start_count_down():
	time_counter = WAIT_TIME
	countdown = true


func stop_count_down():
	time_counter = WAIT_TIME
	countdown = false


func encode(buffer:PackedByteArray):
	var start_index = buffer.size()
	buffer.resize(buffer.size() + 2)
	buffer.encode_u8(start_index, countdown)#bool
	if countdown:
		buffer.encode_u8(start_index + 1, int(time_counter) + 1)
	else:
		buffer.encode_u8(start_index + 1, int(time_counter))
	return buffer


func decode(start_index:int, buffer:PackedByteArray):
	countdown = bool(buffer.decode_u8(start_index))
	time_counter = buffer.decode_u8(start_index + 1)
	count_down_label.text = str(time_counter)
	counter_start_label.text = str(time_counter)
	if time_counter > 3:
		count_down_label.visible = true
		counter_start_label.visible = false
	else:
		if not countdown_player.playing:
			countdown_player.play()
		count_down_label.visible = false
		counter_start_label.visible = true
