extends Control
class_name Client

@onready var clientSyncV2: ClientSyncV2 = $SyncV2


var game_scene = null
var connected = false
var connecting = false
var remote_parent = null

signal connect_success
signal disconnected
signal error_connecting

var my_data = {
	"backend_id": DataSaver.get_item("id", -1),
	"id": "",
	"selection": {
		
		"name": Selector.my_name,
		"character": ""
	},
	"d": {
		"c": {
			"i": Vector2(0, 0),
			"j": false
		},
		"title": 255,
		"right_hand": 255,
		"vehicle": 255,
		"reserved": 255,
	},
	"server": {
		
	}
}
var my_player_scene: Character
var im_server_authoritive = false

var server
var remote_players_data = {}
var remote_players_scenes = {}
var character_visibility = {}

var all_players_selection_data ={}

var ping_id = 0
var ping_ms = -1
var ping_time = 0
var i_qualified = false #For qualified map
var i_eliminated = false#For Eliminated map
var spector_player_index = 0
var spector_mode = false


var network_visibility_manager : NetworkVisibilityManager
var lobby_players_joined = 0
var lobby_players_start = 0
var lobby_timer = 0

func _ready():
	multiplayer.connected_to_server.connect(self._connected_ok) #Client
	multiplayer.connection_failed.connect(self._connected_fail) #Client
	multiplayer.server_disconnected.connect(self._server_disconnected) #Client


var connecting_timer = 0
var connecting_timeout = 3


var last_my_data_sync_time = 0
func _physics_process(delta):
	if Constants.is_server:
		return
	
	if connected == false:
		return
	
	send_my_data(delta)
	if connecting:
		connecting_timer += delta
		if connecting_timer > connecting_timeout:
			print("fail from process")
			_connected_fail()

	time_from_last_sync += delta
	if last_sync_arrive_time > last_sync_client_time:
		last_sync_client_time = last_sync_arrive_time
		sync_players_on_physic_process()


var my_data_counter = 0
func send_my_data(delta):
	if Constants.is_server:
		return
	if not connected:
		return
	if server == null:
		return
	
	if Selector.selected_game_mode == Constants.GameMode.Race:
		if server.state == Constants.ServerState.Lobby or server.state == Constants.ServerState.CountDown:
			return
	else: #FreeRide
		if game_scene.client_state != 2: # 2=CLIENT_STATE.Game
			return
	my_data_counter += delta
	if my_data_counter < Constants.SERVER_MY_CONTROLL_SYNC:
		return
	
	if StageManager.me_eliminated:
		return

	while len(my_player_scene.clientNetworkHistory.input_snapshots) > 0:
		var input_state = my_player_scene.clientNetworkHistory.input_snapshots.pop_back()
		if input_state == null:
			break

		my_data_counter -= Constants.SERVER_MY_CONTROLL_SYNC
		my_data["d"]["c"] = {}
		my_data["d"]["c"]["i"] = input_state["input"]
		my_data["d"]["c"]["l"] = input_state["direction"]
		my_data["d"]["c"]["j"] = input_state["jump"]
		my_data["d"]["p"] = my_player_scene.global_position
		my_data["d"]["R"] = my_player_scene.global_rotation
		my_data["d"]["v"] = my_player_scene.velocity
		my_data["d"]["a"] = my_player_scene.get_current_animation()
		my_data["d"]["s"] = my_player_scene.state
		if VehicleManager.ami_in_vehicle():
			my_data["d"]["s"] = 255#In Vehicle
		my_data["d"]["r"] = my_player_scene.ragdoll
		my_data["d"]["title"] = JobManager.my_job_id()
		my_data["d"]["vehicle"] = 255
		my_data["d"]["vehicle_sit"] = 255
		my_data["d"]["right_hand"] = 255
		my_data["d"]["reserved"] = 255
		if Selector.selected_game_mode == Constants.GameMode.FreeRide:
			var inventoryContainer = (game_scene as FreeRide).player_inventory_container
			my_data["d"]["right_hand"] = inventoryContainer.get_hand_network_data()[0]
			my_data["d"]["reserved"] = inventoryContainer.get_hand_network_data()[1]


		var packet = Constants.convert_my_player_controller_to_packed_byte_array(my_data, input_state["tick"])
		if server.LATENCY:
			await get_tree().create_timer(randi_range(server.MIN_FAKE_LATENCY, server.MAX_FAKE_LATENCY) / 1000.0).timeout
		if connected == false or ClientRPC.peer == null:
			return
		if ClientRPC.peer.get_connection_status() != ClientRPC.peer.CONNECTION_CONNECTED:
			continue
		multiplayer.rpc(1, server, "update_player_data", [packet])


func _connected_ok():
	print("Connected ok: ", multiplayer.get_unique_id())
	emit_signal("connect_success")


func init_after_connect():
	connected = true
	connecting = false
	if Constants.is_headless:
		my_data["backend_id"] = -randi() % 1000
		my_data["selection"]["name"] = "NPC#" + str(randi() % 10000)
	
	var name_str = my_data["selection"]["name"]
	var MAX_LENGTH = 12
	if name_str.length() >= MAX_LENGTH:
		name_str = name_str.substr(0, MAX_LENGTH)
		my_data["selection"]["name"] = name_str
	my_data["version"] = NativeFunctions.getBuildNumber()
	
	if StageManager.current_stage == 1:
		if Selector.free_admin:
			my_data["admin"] = true
		multiplayer.rpc(1, server, "new_player", [my_data])


func _connected_fail():
	print("Connected fail")
	connected = false
	connecting = false
	connecting_timer = 0
	emit_signal("error_connecting")


func _server_disconnected():
	connected = false
	print("server disconnected")
	emit_signal("disconnected")


func find_player_data_by_id(id):
	if remote_players_data.has(id):
		return remote_players_data[id]
	return null


func new_player(player_data):
	if connected == false:
		return

	var id = player_data["id"]
	if find_player_data_by_id(id) != null:
		#Already added
		return
	
	if StageManager.current_stage == 1:
		print("new player data from server: ", id)
	var new_data = player_data
	remote_players_data[id] = new_data
	
	#Create player scene
	if not new_data.has("selection"):
		if StageManager.current_stage > 1:
			if StageManager.client_players_data.has(id):
				new_data = StageManager.client_players_data[id]
			else:
				return
		else:
			return
	
	var path = new_data["selection"]["character"]
	if path == null:
		return
	all_players_selection_data[id] = new_data
	var scene = null
	if ResourceLoader.exists(path):
		scene = load(path)
	else:
		scene = load(Selector.get_random_character_path())
	remote_players_scenes[id] = scene.instantiate()
	remote_players_scenes[id].name = str(id)
	remote_players_scenes[id].player_id = id
	remote_parent.add_child(remote_players_scenes[id])
	remote_players_scenes[id].make_remote()
	remote_players_scenes[id].set_character_name(new_data["selection"]["name"])
	remote_players_scenes[id].server = server


func iam_ready():
	if connected == false:
		return

	multiplayer.rpc(1, server, "iam_ready", [])


var time_from_last_sync = 0
var last_players_data_packed = null
var last_sync_arrive_time = 0
var last_sync_client_time = 0
func sync_players(players_data_packed):
	if connected == false:
		return

	last_players_data_packed = players_data_packed
	last_sync_arrive_time = Time.get_ticks_msec()


#All players except me!
func sync_players_on_physic_process():
	if connected == false:
		return

	var players_data_packed = last_players_data_packed
	time_from_last_sync = 0
	var players_data = {}
	for key in players_data_packed:
		players_data[key] = Constants.convert_packed_byte_array_to_player_data(players_data_packed[key])
	for key in players_data.keys():
		var data = players_data[key]
		if data["id"] == multiplayer.get_unique_id():
			###It's me!
			###handled by sync_my_data
			continue
		else:
			new_player(data) #if exists not adding!
			for value_key in data:
				#players_data[key][value_key] = data[value_key]
				remote_players_data[data["id"]][value_key] = data[value_key]
			
			if remote_players_scenes.has(data["id"]):
				update_player(remote_players_scenes[data["id"]], data)
	for key in remote_players_data.keys():
		if not players_data.has(key):
			#Player Removed from server should remove from client
			
			if remote_players_scenes.has(key):
				if StageManager.current_stage == 1 and server.state == Constants.ServerState.Lobby:
					print("another player disconnected: ", key)
					remote_players_scenes[key].queue_free()
					remote_players_scenes.erase(key)
					remote_players_data.erase(key)
				else:
					if is_eliminated(key):
						continue
					print("another player disconnected: ", key)
					if is_instance_valid(remote_players_scenes[key]):
						remote_players_scenes[key].queue_free()
					tag_eliminate(key)
					tag_dc(key)


func update_player(remote_scene: Character, remote_data, its_me=false):
	if Constants.is_server:
		return
	if connected == false:
		return
	if not is_instance_valid(remote_scene):
		return

	var data = remote_data["d"]
	if data["p"] != null and data["vehicle"] == 255:
		var diff = (remote_scene.global_position - data["p"]).length()
		if  diff >= 2:
			remote_scene.global_position = data["p"]
		elif diff >= 0.01:
			if its_me:
				remote_scene.global_position = lerp(remote_scene.global_position, data["p"], Constants.POSITION_LERP)
			else:
				remote_scene.global_position = lerp(remote_scene.global_position, data["p"], 0.5)
	
	#Handle Vehicle
	update_player_in_vehicle(remote_scene, data)
	#print("after: ", data["vehicle"])
	
	
	if data.has("R"):
		if its_me:
			#Don't need
			pass
		else:
			#remote_scene.global_rotation = remote_scene.global_rotation.lerp(data["R"], 0.8)
			if data["vehicle"] == 255:
				remote_scene.global_rotation = data["R"]
			remote_scene.update_remote_cosmetic(Constants.player_sync_data_to_cosmetics(remote_data))
	remote_scene.state = data["s"]
	if remote_scene.state == remote_scene.State.STATE_FREEZE or remote_scene.state == remote_scene.State.STATE_DEAD:
		remote_scene.disable_gravity()
	else:
		remote_scene.enable_gravity()
	if remote_scene.ragdoll == false and data["r"] == true:
		remote_scene.reset_ragdoll_mode()
	var server_animation = remote_scene.find_animation_by_id(data["a"])
	if remote_scene.animation_player.current_animation != server_animation and server_animation != "":
		if remote_scene.state == 7: #Animation
			if remote_scene.animation_should_loop(server_animation):
				remote_scene.animation_player.stop(false)
		if server_animation == remote_scene.SLIDE_ANIMATION:
			remote_scene.animation_player.play(server_animation, -1, Constants.CHARACTER_SLIDE_ANIMATION_SPEED)
			remote_scene.last_animation_played_id = remote_scene.find_animation_id(server_animation)
		elif server_animation == remote_scene.attack_animations[0]:
			remote_scene.animation_player.stop(false)
			remote_scene.animation_player.play(server_animation, -1, Constants.CHARACTER_ATTACK_ANIMATION_SPEED)
			remote_scene.last_animation_played_id = remote_scene.find_animation_id(server_animation)
		elif server_animation in remote_scene.not_loop_list:
			if remote_scene.last_animation_played_id != remote_scene.find_animation_id(server_animation):
				remote_scene.animation_player.play(server_animation, -1, Constants.CHARACTER_ATTACK_ANIMATION_SPEED)
				remote_scene.last_animation_played_id = remote_scene.find_animation_id(server_animation)
		else:
			if not remote_scene.animation_should_loop(server_animation):
				var b = remote_scene.last_animation_played_id == remote_scene.find_animation_id(server_animation)
				if b:
					pass
				else:
					remote_scene.animation_player.play(server_animation)
					remote_scene.last_animation_played_id = remote_scene.find_animation_id(server_animation)
			else:
				remote_scene.animation_player.play(server_animation, -1, remote_scene.animation_speed(server_animation))
				remote_scene.last_animation_played_id = remote_scene.find_animation_id(server_animation)
			
	remote_scene.ragdoll = data["r"]
	if data.has("v"):
		remote_scene.velocity = data["v"]


func update_player_in_vehicle(remote_scene:Character, data):
	remote_scene.animation_player.speed_scale = 1.0
	if data["vehicle"] != 255:#in vehicle
		var vehicle = VehicleManager.find_vehicle_by_id(data["vehicle"])
		if vehicle == null:
			return #Vehicle not loaded
		
		vehicle = vehicle["vehicle"]
		
		var sit = vehicle.get_sit(data["vehicle_sit"]) as VehicleSit
		sit.mount_player(remote_scene.player_id)
		remote_scene.remote_vehicle = vehicle
		remote_scene.remote_sit = sit
		remote_scene.animation_player.speed_scale = sit.animtion_speed()
		
		for key in remote_players_scenes:
			var rp:Character = remote_players_scenes[key]
			rp.add_collision_exception_with(remote_scene)
		my_player_scene.add_collision_exception_with(remote_scene)
	else:
		remote_scene.remote_vehicle = null
		remote_scene.remote_sit = null
		for key in remote_players_scenes:
			var rp = remote_players_scenes[key]
			if not is_instance_valid(rp):
				continue
			rp.remove_collision_exception_with(remote_scene)
		my_player_scene.remove_collision_exception_with(remote_scene)
		
		VehicleManager.unmount_remote_player_in_client(remote_scene.player_id)


func ping():
	if Constants.is_server:
		return
	if not connected or ClientRPC.peer == null:
		return
	ping_id += 1
	ping_time = Time.get_ticks_msec()
	if not Constants.is_multiplayer_connected():
		return
	multiplayer.rpc(1, server, "ping", [ping_id, Engine.get_frames_per_second(), ping_ms])


func _on_ping_timer_timeout():
	if connected == false:
		return
	ping()


func jump():
	#multiplayer.rpc(1, server, "jump", [])
	pass


func set_my_player_scene(scene, path, character_id):
	my_player_scene = scene
	my_data["selection"]["character"] = path
	my_data["selection"]["character_id"] = character_id
	all_players_selection_data[multiplayer.get_unique_id()] = my_data


func tag_qualified_me():
	my_data["server"]["eliminated"] = false
	my_data["server"]["qualified"] = true


func tag_eliminate_me():
	my_data["server"]["eliminated"] = true
	my_data["server"]["qualified"] = false
	my_data["server"]["last_stage"] = StageManager.current_stage


func tag_qualified(key):
	if not remote_players_data[key].has("server"):
		remote_players_data[key]["server"] = {}
	remote_players_data[key]["server"]["eliminated"] = false
	remote_players_data[key]["server"]["qualified"] = true


func tag_eliminate(key):
	if not remote_players_data.has(key):
		return
	if not remote_players_data[key].has("server"):
		remote_players_data[key]["server"] = {}
	remote_players_data[key]["server"]["eliminated"] = true
	remote_players_data[key]["server"]["qualified"] = false
	remote_players_data[key]["server"]["last_stage"] = StageManager.current_stage


func tag_dc(key):
	if not remote_players_data[key].has("server"):
		remote_players_data[key]["server"] = {}
	remote_players_data[key]["server"]["dc"] = true


func is_dc(key):
	if not remote_players_data[key].has("server"):
		return false
	
	if not remote_players_data[key]["server"].has("dc"):
		return false
	
	return remote_players_data[key]["server"]["dc"]


func is_eliminated(key):
	if not remote_players_data[key].has("server"):
		return false
	
	if not remote_players_data[key]["server"].has("eliminated"):
		return false
	
	return remote_players_data[key]["server"]["eliminated"]
