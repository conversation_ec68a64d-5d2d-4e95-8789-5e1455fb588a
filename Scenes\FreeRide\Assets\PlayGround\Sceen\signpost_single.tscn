[gd_scene load_steps=7 format=4 uid="uid://b7srlfcpw2daj"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_tl4g3"]
cull_mode = 1
albedo_color = Color(0, 0, 0, 1)
grow = true
grow_amount = 0.015

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_jv5ep"]
next_pass = SubResource("StandardMaterial3D_tl4g3")
cull_mode = 2
diffuse_mode = 3
specular_mode = 1
albedo_color = Color(0.572549, 0.294118, 0.188235, 1)
roughness = 0.5

[sub_resource type="ArrayMesh" id="ArrayMesh_3s03b"]
_surfaces = [{
"aabb": AABB(-0.898218, -1.08717e-08, -0.199823, 1.66909, 3.68064, 0.343833),
"format": 34359742465,
"index_count": 144,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACQAIAAQABAAGAAkADAAKAAsACwANAAwACwAOAA8ADwANAAsABQAAAAIAAgAHAAUAEgAQABEAEQATABIAEgAUABUAFQAQABIADAAWABcACgAMABcACgAXABgAFQAKABgAFQAYABEAEQAQABUACgAVABQAFAALAAoAFAAOAAsADgAUABIAEgAIAA4AEwAIABIACAAJAA4ACQAZAA4AGQAPAA4AGQAWAAwADAANABkADQAPABkAFwADAAEAAQAYABcABAAIABMAEwAFAAQAEwARAAUAAAAFABEAAAARABgAGAABAAAAAgADABcAFwAWAAIAFgAZAAIAGQAHAAIABgAHABkAGQAJAAYA"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 26,
"vertex_data": PackedByteArray("vHQTPjvGOjK8dBM+vHQTPjvGOrK8dBO+vHQTvjvGOjK8dBM+vHQTvjvGOrK8dBO+Yr68PZuPa0CHvry9Yr68PZuPa0A9vrw9Yr68vZuPa0CHvry9Yr68vZuPa0A9vrw933rDPWGdXEACe8O9vPrCvaa5XUDf+sK9jXYSvwc0MkBMnky+pPFlv59AS0BOnky+jXYSvwc0MkCKEoO9pPFlv59AS0COEoO9Qr8Bv2gfYEBQnky+Qr8Bv2gfYECREoO9QKA0P+nCKkCJEoO901/YPW1BLkCKEoO9i1dFP0muWECQEoO933rDPWGdXECREoO9i1dFP0muWEBPnky+QKA0P+nCKkBMnky+BNLXvRd8L0CKEoO9BNLXvRd8L0Ag0te901/YPW1BLkDvX9i9vPrCvaa5XUCREoO9")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_wbpj0"]
resource_name = "SignpostSingle_SignpostSingle"
_surfaces = [{
"aabb": AABB(-0.898218, -1.08717e-08, -0.199823, 1.66909, 3.68064, 0.343833),
"attribute_data": PackedByteArray("1Wo1P1YqlT7VajU/CEeBP9VqNb8IR4E/1Wo1P2q12j/VajW/CEeBP9VqNT8IR4E/1Wo1PwhHgT/VajW/ViqVPtVqNb8IR4E/1Wo1vwhHgT/VajW/arXaP9VqNT8IR4E/1DbovnPXiMHUNui+tQ26P9Q26D5z14jB1DboPnPXiMHUNui+luQLP9Q26L5z14jB1DbovnPXiMHUNug+c9eIwdQ26D61Dbo/1DboPnPXiMHUNui+c9eIwdQ26D6W5As/gYDwvvRKf8GBgPA+cMCOvYGA8D70Sn/B4OLvvmFUgMHg4u8+YVSAweDi7z7wg4G/p757v4RHNcGnvnu//u9CwBoyNED/PkvBp757v49kXcGnvnu/YHazwKVzjUCCEGrBhEKhvoRHNcGEQqG+/u9CwBoyNMD/PkvBhEKhvo9kXcGEQqG+YHazwKVzjcCCEGrBp757vzTYAcE8oR9A6N6Bwae+ez/+70LAhEKhvjTYAcE8oR/A6N6BwYRCoT7+70LAhEKhvu4lUUAROl5AGhdCwYRCoT7EQkbBhEKhvghVmz6xGgU/qGNGwYRCoT46QEbB88pyQOqVesGEQqE+7iVRQIRCoT5g/X7BgYDwPvRsf8GEQqE+cMCOvYRCoT70Sn/B88pywOqVesGnvns/7iVRQKe+ez9g/X7Bp757v+4lUUAROl7AGhdCwae+ez/EQkbBhEKhvmHDR8GEQqG+7Ew9v3PDBL/F5kfBc8MEv2HDR8FzwwS/7Ew9v3PDBD9hw0fBsRoFvwhVmz6xGgW/OkBGwbEaBT86QEbBhEKhvmFUgMHg4u++W2WAwYRCoT7wg4G/"),
"format": 34359742487,
"index_count": 144,
"index_data": PackedByteArray("BwAAAAMAAwAKAAcAFAANABAAEAAXABQAHAAYAAwADAATABwAJAAeACEAIQAnACQAIgAqAC0ALQAoACIADwABAAgACAAWAA8ANgAxADQANAA5ADYAOAA+AEEAQQAyADgAJQBDAEYAHwAlAEYAHwBGAEgAPwAfAEgAPwBIADMAMwAwAD8AIABAADwAPAAjACAAPAArACMALAA9ADcANwAZACwAOgAZADcAGQAdACwAHQBNACwATQAvACwATABEACYAJgApAEwAKQAuAEwARwALAAQABABJAEcADgAaADsAOwARAA4AOwA1ABEAAgARADUAAgA1AEoASgAFAAIABgAJAEUARQBCAAYAQgBLAAYASwAVAAYAEgAVAEsASwAbABIA"),
"material": SubResource("StandardMaterial3D_jv5ep"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 78,
"vertex_data": PackedByteArray("vHQTPjvGOjK8dBM+vHQTPjvGOjK8dBM+vHQTPjvGOjK8dBM+vHQTPjvGOrK8dBO+vHQTPjvGOrK8dBO+vHQTPjvGOrK8dBO+vHQTvjvGOjK8dBM+vHQTvjvGOjK8dBM+vHQTvjvGOjK8dBM+vHQTvjvGOrK8dBO+vHQTvjvGOrK8dBO+vHQTvjvGOrK8dBO+Yr68PZuPa0CHvry9Yr68PZuPa0CHvry9Yr68PZuPa0CHvry9Yr68PZuPa0A9vrw9Yr68PZuPa0A9vrw9Yr68PZuPa0A9vrw9Yr68vZuPa0CHvry9Yr68vZuPa0CHvry9Yr68vZuPa0CHvry9Yr68vZuPa0A9vrw9Yr68vZuPa0A9vrw9Yr68vZuPa0A9vrw933rDPWGdXEACe8O933rDPWGdXEACe8O933rDPWGdXEACe8O9vPrCvaa5XUDf+sK9vPrCvaa5XUDf+sK9vPrCvaa5XUDf+sK9jXYSvwc0MkBMnky+jXYSvwc0MkBMnky+jXYSvwc0MkBMnky+pPFlv59AS0BOnky+pPFlv59AS0BOnky+pPFlv59AS0BOnky+jXYSvwc0MkCKEoO9jXYSvwc0MkCKEoO9jXYSvwc0MkCKEoO9pPFlv59AS0COEoO9pPFlv59AS0COEoO9pPFlv59AS0COEoO9Qr8Bv2gfYEBQnky+Qr8Bv2gfYEBQnky+Qr8Bv2gfYEBQnky+Qr8Bv2gfYECREoO9Qr8Bv2gfYECREoO9Qr8Bv2gfYECREoO9QKA0P+nCKkCJEoO9QKA0P+nCKkCJEoO9QKA0P+nCKkCJEoO901/YPW1BLkCKEoO901/YPW1BLkCKEoO901/YPW1BLkCKEoO9i1dFP0muWECQEoO9i1dFP0muWECQEoO9i1dFP0muWECQEoO933rDPWGdXECREoO933rDPWGdXECREoO933rDPWGdXECREoO9i1dFP0muWEBPnky+i1dFP0muWEBPnky+i1dFP0muWEBPnky+QKA0P+nCKkBMnky+QKA0P+nCKkBMnky+QKA0P+nCKkBMnky+BNLXvRd8L0CKEoO9BNLXvRd8L0CKEoO9BNLXvRd8L0CKEoO9BNLXvRd8L0Ag0te9BNLXvRd8L0Ag0te9BNLXvRd8L0Ag0te901/YPW1BLkDvX9i901/YPW1BLkDvX9i901/YPW1BLkDvX9i9vPrCvaa5XUCREoO9vPrCvaa5XUCREoO9vPrCvaa5XUCREoO9/38AAP///7//f8eB////vzf+x4H+/wCA/38AAP///783/v//AAD/vzf+x4H/////xwHHgf9//7//fwAA////v/9/x4H///+/xwHHgf9//7//fwAA////vzf+//8AAP+/N/7//wAA/7//f///AAD/vzf+x4H//////3/Hgf///7//f///AAD/vzf+x4EAAP//xwHHgf9//783/v//AAD/v/9///8AAP+/xwHHgf9//7//f8eB////v/9///8AAP+/N/7//wAA/7+rilL1AAD//zf+x4H/////xwHHgf9//783/v//AAD/v6uKUvUAAP7/LDrTRf9//79TdawK/3//v/////8AAP+/LDrTRf9//7/TRdLF/3//v/////8AAP+/LDrTRf9//79TdawK/3//v/9//3////+/LDrTRf9//7/TRdLF/3//v/9//3/+//+/00XSxf9//7//////AAD/v6uKUvUAAP7/00XSxf9//7//f/9//v//v6uKUvX/////U3WsCv9//7//f/9//v//v1L1U3X/////U3WsCv9//7//f/9//v//vzf+x4H+/wCA/3//f/7//7+rilL1//8AgFL1U3X//////3//f/7//7+rilL1/////zf+x4EAAP///////wAA/7+rilL1/////1L1U3X/////U3WsCv9//7//////AAD/v1L1U3X/////xwHHgf9//79TdawK/3//v/9//3/+//+/xwHHgf9//79TdawK/3//vzf+//8AAP+/U3WsCv9//783/v//AAD/vzf+x4H/////xwHHgf9//7//f/9//v//v6uKUvX/////")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_3s03b")

[sub_resource type="BoxShape3D" id="BoxShape3D_8wq0i"]
size = Vector3(0.368184, 3.66271, 0.305043)

[sub_resource type="BoxShape3D" id="BoxShape3D_2frla"]
size = Vector3(1.61673, 0.865543, 0.167403)

[node name="SignpostSingle" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_wbpj0")
skeleton = NodePath("")
surface_material_override/0 = SubResource("StandardMaterial3D_jv5ep")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.85653, 0)
shape = SubResource("BoxShape3D_8wq0i")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(0.99428, 0.106802, 0, -0.106802, 0.99428, 0, 0, 0, 1, -0.0558388, 3.08154, -0.127196)
shape = SubResource("BoxShape3D_2frla")
