extends StateMachineState
class_name VehicleRaceResultState

@onready var LobbyState: Node = $"../Lobby"
@onready var race_state_machine: VehicleStateMachine = $".."
@onready var vehicle_race: Node3D = $"../.."


var exit_counter = 0
var exit_time = 1#Seconds
var client_shown_result = false


func on_enter() -> void:
	client_shown_result = false
	exit_counter = 0
	if Constants.is_server:
		var data = {
			"players": [],
			"minigame_id": vehicle_race.minigame_backend_id,
		}
		for p_data in race_state_machine.ranks:
			var player_data = {
				"backend_id": p_data["backend_id"],
				"coin": p_data["coin"],
				"cup": 0,
				"smart": p_data["smart"],
				"winner": p_data["rank"] == 1,
				"time": p_data["time"],
			}
			data["players"].append(player_data)
		data["godot_id"] = vehicle_race.id
		BackendManager.send_end_minigame_request(data)


func on_process(delta: float) -> void:
	if Constants.is_server:
		exit_counter += delta
		if exit_counter > exit_time:
			for p in race_state_machine.players:
				if p != null:
					race_state_machine.i_exited.rpc_id(p)
			race_state_machine.players = [null, null, null, null]
			race_state_machine.current_state = LobbyState


func on_physics_process(_delta: float) -> void:
	pass


func on_input(_event: InputEvent) -> void:
	pass


func on_exit() -> void:
	pass


func encode(buffer:PackedByteArray):
	var _start_index = buffer.size()
	return buffer


func decode(_start_index:int, _buffer:PackedByteArray):
	pass


func _on_exitable_control_touched() -> void:
	race_state_machine.on_player_exit.rpc_id(1)
