[gd_scene load_steps=7 format=3 uid="uid://bhbnsvej1u3k1"]

[ext_resource type="StyleBox" uid="uid://bblxlot03sp7l" path="res://Scenes/ui/row_panel_bg.tres" id="1_rmgiv"]
[ext_resource type="Script" path="res://Scenes/FreeRide/Assets/Buildings/RaceMap/VehicleRaceRankRow.gd" id="2_0ygvy"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="3_5u17p"]
[ext_resource type="Texture2D" uid="uid://bwpbkk86b7quq" path="res://Scenes/ui/assets/smart.png" id="4_418pd"]
[ext_resource type="Texture2D" uid="uid://8q8ki5ld0acy" path="res://Scenes/ui/assets/Coin.png" id="5_coirg"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_oavkp"]
bg_color = Color(1, 0.980392, 0.952941, 0.560784)
corner_radius_top_left = 30
corner_radius_top_right = 30
corner_radius_bottom_right = 30
corner_radius_bottom_left = 30

[node name="PlayerRankRow" type="Panel"]
custom_minimum_size = Vector2(760, 100)
anchors_preset = 10
anchor_right = 1.0
offset_left = 43.0
offset_right = -407.0
offset_bottom = 100.0
grow_horizontal = 2
mouse_filter = 1
theme_override_styles/panel = ExtResource("1_rmgiv")
script = ExtResource("2_0ygvy")

[node name="MePanel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_oavkp")

[node name="RankLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 9
anchor_bottom = 1.0
offset_left = 17.0
offset_right = 40.0
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("3_5u17p")
theme_override_font_sizes/font_size = 30
text = "100"
horizontal_alignment = 1
vertical_alignment = 1

[node name="NameLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 71.0
offset_right = -68.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("3_5u17p")
theme_override_font_sizes/font_size = 30
text = "PlayerName"
vertical_alignment = 1

[node name="TimeLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -8.5
offset_top = -24.0
offset_right = 8.5
offset_bottom = 24.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("3_5u17p")
theme_override_font_sizes/font_size = 30
text = "10:00"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 304.0
grow_horizontal = 2
grow_vertical = 2
alignment = 2

[node name="SmartLabel" type="Label" parent="HBoxContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("3_5u17p")
theme_override_font_sizes/font_size = 30
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="SmartTexture" type="TextureRect" parent="HBoxContainer"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
texture = ExtResource("4_418pd")
expand_mode = 1
stretch_mode = 5

[node name="CoinLabel" type="Label" parent="HBoxContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("3_5u17p")
theme_override_font_sizes/font_size = 30
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CoinTexture" type="TextureRect" parent="HBoxContainer"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
texture = ExtResource("5_coirg")
expand_mode = 1
stretch_mode = 5
