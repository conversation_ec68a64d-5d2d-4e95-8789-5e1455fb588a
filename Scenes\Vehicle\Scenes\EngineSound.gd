extends AudioStreamPlayer3D
class_name EngineSound

@export var vehicle : Vehicle
@export var sample_rpm := 4000.0
@export var disabled = false

func _ready() -> void:
	if disabled:
		stop()


func _physics_process(_delta):
	if disabled:
		return
	if Constants.is_server:
		return
	pitch_scale = vehicle.motor_rpm / sample_rpm
	var vol = (vehicle.throttle_amount * 0.5) + 0.5
	vol *= DataSaver.get_item("sound_volume")
	volume_db = linear_to_db(vol)
