[gd_scene load_steps=4 format=3 uid="uid://dq8xld01sre37"]

[ext_resource type="Shader" uid="uid://58v8kr6gx4wv" path="res://assets/characters/Ghostrider/Fire.tres" id="1_0sjcv"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_er580"]
render_priority = 0
shader = ExtResource("1_0sjcv")
shader_parameter/Vector2Parameter = Vector2(0, 1)

[sub_resource type="QuadMesh" id="QuadMesh_722e4"]
size = Vector2(1.2, 1.5)

[node name="Fire" type="Node3D"]

[node name="CPUParticles3D" type="CPUParticles3D" parent="."]
material_override = SubResource("ShaderMaterial_er580")
amount = 1
lifetime = 0.01
mesh = SubResource("QuadMesh_722e4")
gravity = Vector3(0, 0, 0)
color = Color(4, 0.8, 0, 1)
