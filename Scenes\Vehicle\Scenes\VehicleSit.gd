extends Node3D
class_name VehicleSit


@export var is_driver = false
@export var animation:String = ""

@onready var sit: Marker3D = $Sit
@onready var look: Marker3D = $Look


var playerId = null
var index = 0


func _ready() -> void:
	pass # Replace with function body.


func animtion_speed():
	var vehicle = get_parent().get_parent()
	return clamp(abs(vehicle.speed_kmh() / 30), 0, 1)


func mount_player(id):
	playerId = id


func unmount_player():
	playerId = null


func is_empty():
	return playerId == null


func sit_position():
	return sit.global_position


func look_position():
	return look.global_position
