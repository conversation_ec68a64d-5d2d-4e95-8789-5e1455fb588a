extends CharacterBody3D
class_name ClientSyncVehicle


@export var wheels: Array[Node3D]
@export var wheels_z: Array[float]
@export var wheel_speed_ratio = 1.0

@export var steering: Node3D
@export var max_steering_rotation: float = 16
@export var light3d: Node
@export var body_mesh: Node
@export var brake_mat_index = 4
@export_enum("X", "Y", "Z") var wheel_rotation: int = 0

@onready var sits_parent: Node3D = $SitsParent
@onready var hud: ExitableControl = $HUD
@onready var vcontainer: VBoxContainer = $HUD/Panel/VBoxContainer
@onready var camera_look: Marker3D = $CameraLook
@onready var exit_pos: Node3D = $ExitPos


const SIT_BUTTON = preload("res://Scenes/Vehicle/Scenes/SitButton.tscn")

var NetworkID: int#1 byte id
var TypeID: int
var OwnerID: int

func _ready() -> void:
	exit_hud()
	var index = 0
	for sit:VehicleSit in sits_parent.get_children():
		sit.index = index
		index += 1
		
		var btn = SIT_BUTTON.instantiate()
		vcontainer.add_child(btn)
		btn.set_sit(sit)
		btn.pressed.connect(on_sit_button_pressed)


func _process(delta: float) -> void:
	var index = 0
	for w in wheels:
		match wheel_rotation:
			0: w.rotation.x -= speed_kmh() * delta * wheel_speed_ratio * wheels_z[index]
			1: w.rotation.y -= speed_kmh() * delta * wheel_speed_ratio * wheels_z[index]
			2: w.rotation.z -= speed_kmh() * delta * wheel_speed_ratio * wheels_z[index]
		index += 1


func _physics_process(_delta: float) -> void:
	if Constants.is_server:
		return
	
	
	if last_updated_time != last_network_time:
		last_updated_time = last_network_time
		var diff = (network_position - global_position).length()
		if diff >= 5:
			global_position = network_position
		elif diff >= 1:
			global_position = global_position.lerp(network_position, 0.1)
		
	global_rotation.x = lerp_angle(global_rotation.x, network_rotation.x, 0.4)
	global_rotation.y = lerp_angle(global_rotation.y, network_rotation.y, 0.3)
	global_rotation.z = lerp_angle(global_rotation.z, network_rotation.z, 0.4)
	
	move_and_slide()


func data_size():
	return 24 + 12


var last_network_time = 0
var last_updated_time =  0
func decode(start_index:int, buffer:PackedByteArray) -> int:
	last_network_time = Time.get_ticks_msec()
	
	NetworkID = buffer.decode_u8(start_index)
	TypeID = buffer.decode_u8(start_index + 1)
	OwnerID = buffer.decode_u32(start_index + 2)
	
	network_position.x = buffer.decode_half(start_index + 6) #2bytes
	network_position.y = buffer.decode_half(start_index + 8) #2bytes
	network_position.z = buffer.decode_half(start_index + 10) #2bytes
	
	network_rotation.x = buffer.decode_half(start_index + 12) #2bytes
	network_rotation.y = deg_to_rad(buffer.decode_half(start_index + 14)) #2bytes
	network_rotation.z = buffer.decode_half(start_index + 16) #2bytes
	
	if not is_valid(network_position):
		print("nan handling")
		print(NetworkID, " ", TypeID, "  ", OwnerID, " ", network_position)
		network_position = Vector3(1000, 1000, 1000)
	if not is_valid(network_rotation):
		network_rotation = Vector3(1000, 1000, 1000)
	
	#velocity.x = buffer.decode_half(start_index + 18) #2bytes
	#velocity.y = buffer.decode_half(start_index + 20) #2bytes
	#velocity.z = buffer.decode_half(start_index + 22) #2bytes
	
	network_steering = buffer.decode_half(start_index + 24)
	network_brake = buffer.decode_u8(start_index + 26)
	network_rpm = buffer.decode_u16(start_index + 27)
	
	velocity.x = buffer.decode_half(start_index + 29)
	velocity.y = buffer.decode_half(start_index + 31)
	velocity.z = buffer.decode_half(start_index + 33)
	
	network_light = buffer.decode_u8(start_index + 35)
	
	handle_cosmetics_state()
	return data_size()


func is_sit_empty(sit_index):
	if sit_index < 0 or sit_index > sits_parent.get_child_count():
		return false
	
	return sits_parent.get_child(sit_index).is_empty()


func get_sit(sit_index) -> VehicleSit:
	if sit_index < 0 or sit_index > sits_parent.get_child_count():
		return null
	
	return sits_parent.get_child(sit_index)


func unmount_player(player_id) -> bool:
	for sit:VehicleSit in sits_parent.get_children():
		if sit.playerId == player_id:
			sit.unmount_player()
			return true
	
	return false


func has_driver():
	for sit:VehicleSit in sits_parent.get_children():
		if sit.is_driver:
			return not sit.is_empty()

	return false


func get_driver_sit() -> VehicleSit:
	for sit:VehicleSit in sits_parent.get_children():
		if sit.is_driver:
			return sit

	return null


var network_position = Vector3()
var network_rotation = Vector3()
var network_steering = 0
var network_brake = 0
var network_rpm = 0
var network_light = 0


#Should implement in each vehicle
func handle_cosmetics_state():
	if brake_mat_index != -1:
		var color = Color("e13139")
		if network_brake:
			color = Color("f3494c")
		if body_mesh:
			var inside_mesh = body_mesh.mesh
			var mat1 = inside_mesh.surface_get_material(brake_mat_index).duplicate()
			mat1.albedo_color = color
			body_mesh.set_surface_override_material(brake_mat_index, mat1)
	if steering:
		steering.rotation.y = deg_to_rad(max_steering_rotation) * network_steering + deg_to_rad(-180)
	
	
	if light3d:
		light3d.visible = network_light


func speed_kmh():
	return -(velocity * transform.basis).z * 3.6


var last_action_time = 0
func _on_base_actionable_action() -> void:
	if Selector.in_prison:
		return
	if Time.get_ticks_msec() - last_action_time < 500:
		return
	last_action_time = Time.get_ticks_msec()
	hud.visible = true
	hud.process_mode = Node.PROCESS_MODE_INHERIT
	var freeride = Constants.client.game_scene as FreeRide
	hud.reparent(freeride.hud)


func exit_hud():
	hud.reparent(self)
	hud.visible = false
	hud.process_mode = Node.PROCESS_MODE_DISABLED


func on_sit_button_pressed(sit_index):
	if Selector.in_prison or Selector.am_i_sit:
		exit_hud()
		return
	VehicleManager.mount_vehicle.rpc_id(1, NetworkID, sit_index)
	exit_hud()


func is_valid(v:Vector3):
	if is_nan(v.x):
		return false
	if is_nan(v.y):
		return false
	if is_nan(v.z):
		return false
	
	return true


func get_exit_pos():
	return exit_pos.global_position
