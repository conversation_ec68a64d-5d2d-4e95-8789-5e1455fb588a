extends Panel

@onready var loading: Panel = $Loading
@onready var id: Label = $Selected/DataContainer/ID
@onready var username: Label = $Selected/DataContainer/username
@onready var title: Label = $Selected/DataContainer/Title
@onready var ban_time: Label = $"Selected/DataContainer/Ban Time"
@onready var police_report: Button = $Selected/PoliceReport
@onready var selected_name: Label = $Selected/SelectedName
@onready var search_text_edit: LineEdit = $HBoxContainer/TextBGPanel/SearchTextEdit
@onready var search_http_request: HTTPRequest = $SearchHTTPRequest
@onready var button_container: GridContainer = $Selected/ButtonContainer
@onready var bazras_parent: Control = $"../.."
@onready var admin_parent: Control = $"../../../Admin"
@onready var report_list: Panel = $ReportList


var search_data = {}


func _ready() -> void:
	clear_ui()


func clear_ui():
	police_report.visible = false
	selected_name.text = ""
	id.text = ""
	username.text = ""
	title.text = ""
	ban_time.text = ""
	button_container.visible = false


func update_ui():
	button_container.visible = true
	username.text = search_data["username"]
	selected_name.text = search_data["handle"]
	title.text = search_data["title"]
	ban_time.text = Constants.pretify_seconds(search_data["ban_time"])
	id.text = str(search_data["id"])
	#police_report.visible = search_data["police"]
	police_report.visible = true


func _on_search_button_pressed() -> void:
	loading.visible = true
	clear_ui()
	search_data = {}
	var url = Constants.BACKEND_URL + "/game/free_admin_search/"
	var data = {
		"username": search_text_edit.text,
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	search_http_request.cancel_request()
	search_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_search_http_request_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray) -> void:
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		search_data = json
		update_ui()
	else:
		Constants.show_toast("شناسه غلط است " + str(response_code))
	loading.visible = false
	


func _on_exit_elite_button_pressed() -> void:
	visible = false


func _on_paste_button_pressed() -> void:
	search_text_edit.text = DisplayServer.clipboard_get()


func _on_ban_5m_button_pressed() -> void:
	send_ban_user_request(search_data["username"], 1.0/12)


func _on_ban_30m_button_pressed() -> void:
	send_ban_user_request(search_data["username"], 1.0/2)


func _on_ban_1h_button_pressed() -> void:
	send_ban_user_request(search_data["username"], 1)


func _on_ban_4h_button_pressed() -> void:
	send_ban_user_request(search_data["username"], 4)


func _on_ban_1d_button_pressed() -> void:
	send_ban_user_request(search_data["username"], 24)


func send_ban_user_request(ban_username, time):
	loading.visible = true
	bazras_parent.send_ban_user_request(null, time, ban_username)


func _on_bazras_ban_request_completed() -> void:
	loading.visible = false


func _on_un_ban_button_pressed() -> void:
	loading.visible = true
	bazras_parent.send_unban_user_request(null, search_data["username"])


func _on_clear_title_button_pressed() -> void:
	bazras_parent.send_title_request(null, "", "FFFFFF", search_data["username"])


func _on_alive_me_pressed() -> void:
	admin_parent._on_alive_me_pressed()


func _on_police_report_pressed() -> void:
	if search_data.has("username"):
		report_list.show_popup(search_data["username"])
