extends Node3D

@onready var http_request = $HTTPRequest
@onready var error_hud = $HUD/Error
@onready var error_label = $HUD/Error/label
@onready var hud = $HUD
@onready var has_prize_hud = $HUD/HasPrize
@onready var no_prize_hud = $HUD/NoPrize
@onready var wheel = $HUD/Wheel
@onready var timer_label: Label = $HUD/NoPrize/Label

var is_spinning = false
var MAX_WHEEL_SPEED = 450
var wheel_speed = 0
var prizes = [100, 120,  140, 160, 180, 190, 200, 500]

func _ready():
	_on_exit_button_pressed()
	http_request.request_completed.connect(on_request_complete)
	var index = 0
	for p in prizes:
		wheel.get_child(index).text = str(p)
		index += 1


func _on_actionable_area_action():
	hud.visible = true
	if Constants.has_daily_reward():
		has_prize_hud.visible = true
		no_prize_hud.visible = false
	else:
		has_prize_hud.visible = false
		no_prize_hud.visible = true
	error_hud.visible = false
	set_processes(Node.PROCESS_MODE_INHERIT)
	Constants.show_mouse()


func _process(delta):
	if Constants.is_server:
		return

	if Input.is_action_just_pressed("hide"):
		_on_exit_button_pressed()
	
	if is_spinning:
		wheel.rotation += wheel_speed * delta
		wheel.rotation = Constants.convert_angle_to_lerpable(wheel.rotation)
	
	if hud.visible:
		if no_prize_hud.visible:
			timer_label.text = Constants.pretify_seconds(Constants.next_daily_reward_time())


func send_request():
	var url = Constants.BACKEND_URL + "/shop/daily_reward/"
	var data = {}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	http_request.cancel_request()
	http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func on_request_complete(_result, response_code, _headers, body):
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())

		var prize = json["prize"]
		Firebase.logEvent_EarnVirtualCurrency("coin", prize)
		var now = int(Time.get_unix_time_from_system())
		DataSaver.set_item("daily_claim_time", now)
		create_notification()
		print("PRIZE = ", prize)
		
		GameAnalyitics.addResourceEvent("Source", prize, "reward", "daily")
		
		await Constants.wait_timer(1)
		var stop_time = 7
		var stop_slot = find_prize_slot(prize)
		var tween = get_tree().create_tween()
		is_spinning = false
		var to_rotation = -stop_slot.rotation + 2 * PI * 10
		tween.tween_property(wheel, "rotation", to_rotation, stop_time).set_trans(Tween.TRANS_EXPO).set_ease(Tween.EASE_OUT)
		await Constants.wait_timer(stop_time)
		Selector.my_coin = json["coins"]
		has_prize_hud.visible = false
		no_prize_hud.visible = true
		return

	error_hud.visible = true
	error_label.text = tr("CONNECTION_ERROR")


func create_notification():
	LocalNotification.show("جایزه‌ی روزانه‌ات آماده‌اس.", "جایزه روزانه", Constants.DAILY_REWARD_INTERVAL, LocalNotification.DAILY_NOTIFICATION_TAG)


func _on_exit_button_pressed():
	hud.visible = false
	set_processes(Node.NOTIFICATION_DISABLED)
	Constants.hide_mouse()


func find_prize_slot(prize):
	for label in wheel.get_children():
		if int(label.text) == prize:
			return label
	
	return wheel.get_child(0)


func _on_claim_button_pressed():
	has_prize_hud.visible = false
	is_spinning = true
	wheel_speed = MAX_WHEEL_SPEED
	send_request()


func _on_retry_button_pressed():
	error_hud.visible = false
	_on_claim_button_pressed()


func set_processes(p):
	wheel.process_mode = p
	hud.process_mode = p
