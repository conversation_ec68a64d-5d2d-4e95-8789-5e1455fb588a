[gd_scene load_steps=6 format=4 uid="uid://bpyaj1ju7fq4t"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_o5jhq"]
resource_name = "lambert29"
cull_mode = 2
albedo_color = Color(0.241658, 0.241658, 0.241658, 1)
roughness = 0.635355

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_s3ab6"]
resource_name = "lambert30"
cull_mode = 2
albedo_color = Color(0.492429, 0.492429, 0.492429, 1)
roughness = 0.662878

[sub_resource type="ArrayMesh" id="ArrayMesh_hjy4x"]
_surfaces = [{
"aabb": AABB(-691.155, -615.055, -426.104, 1340.01, 1230.11, 542.446),
"format": 34896613377,
"index_count": 120,
"index_data": PackedByteArray("AQAeACcAAQADAB4AIwACAAAAIwAiAAIAAgAhABEAAgAiACEAIwAOACQAIwAAAA4AEAAnACYAEAABACcAHwADABMAHwAeAAMAJAAPACUAJAAOAA8AJQAQACYAJQAPABAAHwASACAAHwATABIAIAARACEAIAASABEACAAUABUACAAFABQACAAWAAkACAAVABYACQAXAAoACQAWABcAGAAKABcAGAAEAAoABgAYABkABgAEABgABgAaAAsABgAZABoACwAbAAwACwAaABsADAAcAA0ADAAbABwAHQANABwAHQAHAA0AFAAHAB0AFAAFAAcA"),
"name": "lambert29",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("AAD/////AAD///////8AAAAA///a/AAA/////wAAAAAAAAAA2vwAAP//AAAAAAAAAAAAAP//AAD//wAA//8AAOfIAAC3XAAAL48AAKWpAABPUAAAttcAALJLAAD//wAA/38AAP//AABMtAAA//8AALJL/////wAA/3//////AABMtP////8AAE9Q//+21wAAL4///6WpAADnyP//t1wAAP//UCQAAAAA58hQJLdcAAAvj1AkpakAAE9QUCS21wAAAABQJNr8AAAAAFAk//8AALJLUCT//wAA/39QJP//AABMtFAk//8AAP//UCT//wAA//+u2wAAAADnyK7bt1wAAC+PrtulqQAAT1Cu27bXAAAAAK7b2vwAAAAArtv//wAAskuu2///AAD/f67b//8AAEy0rtv//wAA//+u2///AAA=")
}, {
"aabb": AABB(-691.155, -615.055, -426.104, 1340.01, 1230.11, 542.446),
"format": 34896613377,
"index_count": 108,
"index_data": PackedByteArray("CgAMAAkACgALAAwACQANAAgACQAMAA0AAAARAA4AAAACABEABAALAAoABAAGAAsACAAHAAUACAANAAcAEwABABAAEwADAAEADgASAA8ADgARABIADwATABAADwASABMAFQAeAB8AFQAUAB4AFQAgABYAFQAfACAAFgAhABcAFgAgACEAIgAXACEAIgAYABcAGQAiACMAGQAYACIAGQAkABoAGQAjACQAGgAlABsAGgAkACUAGwAmABwAGwAlACYAJwAcACYAJwAdABwAHgAdACcAHgAUAB0A"),
"name": "lambert30",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("AAD/////AAD///////8AAAAA///a/AAA/////wAAAAAAAAAA2vwAAP//AAAAAAAAAAAAAP//AAD//wAA//8AAOfIAAC3XAAAL48AAKWpAABPUAAAttcAALJLAAD//wAA/38AAP//AABMtAAA//8AALJL/////wAA/3//////AABMtP////8AAE9Q//+21wAAL4///6WpAADnyP//t1wAAP//UCQAAAAA58hQJLdcAAAvj1AkpakAAE9QUCS21wAAAABQJNr8AAAAAFAk//8AALJLUCT//wAA/39QJP//AABMtFAk//8AAP//UCT//wAA//+u2wAAAADnyK7bt1wAAC+PrtulqQAAT1Cu27bXAAAAAK7b2vwAAAAArtv//wAAskuu2///AAD/f67b//8AAEy0rtv//wAA//+u2///AAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ipegg"]
resource_name = "Ramp_checkpointglb_Mesh_003"
_surfaces = [{
"aabb": AABB(-691.155, -615.055, -426.104, 1340.01, 1230.11, 542.446),
"attribute_data": PackedByteArray("/18AAP9f////nwAA/5////9f/7//X/+//5//v/+f/7//X/9//x//v//f/7//n/9//1//P/8f////n/8//9////+P/3//f/9/n3H/f+xy/z//f/8/Eo3/P+xyAAD/fwAAEo0AAJ9x/7//f/+//4//v6nK/7//n1SV/49Ulf9/VJWfcVSV/19UlVU1/7//X6oqVTX//+xyqir/f6oqEo2qKv+fqiqpyv//VLX/v/+fqqr/j6qq/3+qqp9xqar/X6mqqkr/v/9fVRWqSv//7HJVFf9/VRUSjVUV/59VFVS1//8="),
"format": 34896613399,
"index_count": 120,
"index_data": PackedByteArray("AwAqADcAAwAGACoAMgAFAAEAMgAwAAUABAAuABkABAAvAC4AMQAWADMAMQAAABYAGAA2ADUAGAACADYALAAHABsALAArAAcAMwAXADQAMwAWABcANAAYADUANAAXABgALAAaAC0ALAAbABoALQAZAC4ALQAaABkAEAAdAB4AEAALAB0AEAAfABEAEAAeAB8AEQAgABIAEQAfACAAIQASACAAIQAIABIADQAiACQADQAJACIADAAlABMADAAjACUAEwAmABQAEwAlACYAFAAnABUAFAAmACcAKAAVACcAKAAOABUAHAAPACkAHAAKAA8A"),
"material": SubResource("StandardMaterial3D_o5jhq"),
"name": "lambert29",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 56,
"vertex_data": PackedByteArray("AAD/////AIAAAP////9U1f///////wCA////////VNUAAP//2vz//wAA///a/FTV/////wAAVNX/////AAD//wAAAADa/P//AAAAANr8VNX//wAAAABU1f//AAAAAP//AAAAAP//AIAAAAAA//9U1f//AAD//wCA//8AAP//VNXnyAAAt1z//y+PAAClqf//T1AAALbX//+ySwAA//8AgP9/AAD//wCATLQAAP//AICyS/////8AgP9//////wCATLT/////AIBPUP//ttf//y+P//+lqf//58j//7dc/////1AkAABU1f//UCQAAP//58hQJLdc//8vj1Akpan//09QUCS21///AABQJNr8//8AAFAk2vxU1QAAUCT//wCAAABQJP//VNWyS1Ak//8AgP9/UCT//wCATLRQJP//AID//1Ak//8AgP//UCT//1TV//+u2wAAVNX//67bAAD//+fIrtu3XP//L4+u26Wp//9PUK7bttf//wAArtva/P//AACu29r8VNUAAK7b//8AgAAArtv//1TVskuu2///AID/f67b//8AgEy0rtv//wCA//+u2///AID//67b//9U1f9///9U1VTV/3///6qqVFX//92KVNVU1aqqVFX//yie///dilTVVNWqqlRV//8onv9///9U1VTV/3///6qqVFX//wic/v/Qav//To3/f////3////9/////f////3////9////+/7By/v/Qav//CJyqqlRV//8onv//CJz+/9Bq//+wcv//3YpU1VTV/3///1TVVNX/f////3////9/////f///qqpUVaqqVFX//yie//8InP7/0Gr+/7By///dilTVVNX/f///VNVU1f9/////f////3////9///+qqlRV")
}, {
"aabb": AABB(-691.155, -615.055, -426.104, 1340.01, 1230.11, 542.446),
"attribute_data": PackedByteArray("/1////+f////X/+//5//v/9f/3//n/9//1//P/+f/z//j/9//3//f59x/3/scv8//3//PxKN/z/scv///3///xKN//+fcf+//3//v/+P/7+pyv+//59Ulf+PVJX/f1SVn3FUlf9fVJVVNf+//1+qKlU1///scqoq/3+qKhKNqir/n6oqqcr//1S1/7//n6qq/4+qqv9/qqqfcamq/1+pqqpK/7//X1UVqkr//+xyVRX/f1UVEo1VFf+fVRVUtf//"),
"format": 34896613399,
"index_count": 108,
"index_data": PackedByteArray("CgAMAAkACgALAAwACQANAAgACQAMAA0AAAARAA4AAAACABEABAALAAoABAAGAAsACAAHAAUACAANAAcAEwABABAAEwADAAEADgASAA8ADgARABIADwATABAADwASABMAFgAjACQAFgAVACMAFgAlABcAFgAkACUAFwAmABgAFwAlACYAJwAYACYAJwAZABgAHAAoACoAHAAaACgAGwArAB0AGwApACsAHQAsAB4AHQArACwAHgAtAB8AHgAsAC0ALgAfAC0ALgAgAB8AIgAhAC8AIgAUACEA"),
"material": SubResource("StandardMaterial3D_s3ab6"),
"name": "lambert30",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 48,
"vertex_data": PackedByteArray("AAD/////Q8D///////+zwAAA///a/FjA/////wAAicMAAAAA2vwAwP//AAAAAInDAAAAAP///7///wAA//+zwOfIAAC3XIrBL48AAKWprsBPUAAAttcjwLJLAAD//x3A/38AAP//O8BMtAAA//9OwLJL/////w3A/3//////CsBMtP//////v09Q//+2113AL4///6WpesHnyP//t1z2wP//UCQAAFTV//9QJAAA///nyFAkt1z//y+PUCSlqf//T1BQJLbX//8AAFAk2vz//wAAUCTa/FTVAABQJP//AIAAAFAk//9U1bJLUCT//wCA/39QJP//AIBMtFAk//8AgP//UCT//wCA//9QJP//VNX//67bAABU1f//rtsAAP//58iu27dc//8vj67bpan//09Qrtu21///AACu29r8//8AAK7b2vxU1QAArtv//wCAAACu2///VNWyS67b//8AgP9/rtv//wCATLSu2///AID//67b//8AgP//rtv//1TVE/cpbpbyLmX69fVrlucuT6gCq37QMJdnAAD/f9Aal3JhJM5tixq5cooNOnl4DMN54BCPdxITdnaM+xp3FPwreP///3+29W9rFe4rXMzwmmGqqlRV//8onv//CJz+/9Bq/v+wcv//3YpU1VTV/3///1TVVNX/f////3////9/////f///qqpUVaqqVFX//yie//8InP7/0Gr+/7By///dilTVVNX/f///VNVU1f9/////f////3////9///+qqlRV")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_hjy4x")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_0x68l"]
data = PackedVector3Array(648.857, 615.055, 116.342, 648.857, 440.548, -426.104, 648.857, 440.548, 116.342, 648.857, 615.055, 116.342, 648.857, 615.055, -426.104, 648.857, 440.548, -426.104, -691.155, 440.548, 116.342, -691.155, 615.055, 109.679, -691.155, 615.055, 116.342, -691.155, 440.548, 116.342, -691.155, 440.548, 109.679, -691.155, 615.055, 109.679, -691.155, 615.055, 109.679, -270.779, 440.548, 30.9794, -270.779, 615.055, 30.9794, -691.155, 615.055, 109.679, -691.155, 440.548, 109.679, -270.779, 440.548, 30.9794, -691.155, 440.548, 116.342, -294.928, 615.055, 116.342, -294.928, 440.548, 116.342, -691.155, 440.548, 116.342, -691.155, 615.055, 116.342, -294.928, 615.055, 116.342, 252.61, 615.055, 116.342, 648.857, 440.548, 116.342, 252.61, 440.548, 116.342, 252.61, 615.055, 116.342, 648.857, 615.055, 116.342, 648.857, 440.548, 116.342, 360.469, 440.548, -229.645, 648.857, 615.055, -426.104, 360.469, 615.055, -229.645, 360.469, 440.548, -229.645, 648.857, 440.548, -426.104, 648.857, 615.055, -426.104, -294.928, 440.548, 116.342, -21.1591, 615.055, 116.342, -21.1591, 440.548, 116.342, -294.928, 440.548, 116.342, -294.928, 615.055, 116.342, -21.1591, 615.055, 116.342, -21.1591, 440.548, 116.342, 252.61, 615.055, 116.342, 252.61, 440.548, 116.342, -21.1591, 440.548, 116.342, -21.1591, 615.055, 116.342, 252.61, 615.055, 116.342, 360.469, 440.548, -229.645, 58.34, 615.055, -66.6336, 58.34, 440.548, -66.6336, 360.469, 440.548, -229.645, 360.469, 615.055, -229.645, 58.34, 615.055, -66.6336, 58.34, 440.548, -66.6336, -270.779, 615.055, 30.9794, -270.779, 440.548, 30.9794, 58.34, 440.548, -66.6336, 58.34, 615.055, -66.6336, -270.779, 615.055, 30.9794, 360.469, -615.055, -229.645, 648.857, -440.566, -426.104, 360.469, -440.566, -229.645, 360.469, -615.055, -229.645, 648.857, -615.055, -426.104, 648.857, -440.566, -426.104, 360.469, -615.055, -229.645, 58.34, -440.566, -66.6336, 58.34, -615.055, -66.6336, 360.469, -615.055, -229.645, 360.469, -440.566, -229.645, 58.34, -440.566, -66.6336, 58.34, -615.055, -66.6336, -270.779, -440.566, 30.9794, -270.779, -615.055, 30.9794, 58.34, -615.055, -66.6336, 58.34, -440.566, -66.6336, -270.779, -440.566, 30.9794, -691.155, -440.566, 109.679, -270.779, -615.055, 30.9794, -270.779, -440.566, 30.9794, -691.155, -440.566, 109.679, -691.155, -615.055, 109.679, -270.779, -615.055, 30.9794, -691.155, -615.055, 116.342, -691.155, -440.566, 109.679, -691.155, -440.566, 116.342, -691.155, -615.055, 116.342, -691.155, -615.055, 109.679, -691.155, -440.566, 109.679, -691.155, -615.055, 116.342, -294.928, -440.566, 116.342, -294.928, -615.055, 116.342, -691.155, -615.055, 116.342, -691.155, -440.566, 116.342, -294.928, -440.566, 116.342, -294.928, -615.055, 116.342, -21.1591, -440.566, 116.342, -21.1591, -615.055, 116.342, -294.928, -615.055, 116.342, -294.928, -440.566, 116.342, -21.1591, -440.566, 116.342, -21.1591, -615.055, 116.342, 252.61, -440.566, 116.342, 252.61, -615.055, 116.342, -21.1591, -615.055, 116.342, -21.1591, -440.566, 116.342, 252.61, -440.566, 116.342, 648.857, -440.566, 116.342, 252.61, -615.055, 116.342, 252.61, -440.566, 116.342, 648.857, -440.566, 116.342, 648.857, -615.055, 116.342, 252.61, -615.055, 116.342, 648.857, -440.566, -426.104, 648.857, -615.055, 116.342, 648.857, -440.566, 116.342, 648.857, -440.566, -426.104, 648.857, -615.055, -426.104, 648.857, -615.055, 116.342, -270.779, -615.055, 30.9794, -21.1591, -615.055, 116.342, 58.34, -615.055, -66.6336, -270.779, -615.055, 30.9794, -294.928, -615.055, 116.342, -21.1591, -615.055, 116.342, 58.34, -615.055, -66.6336, 252.61, -615.055, 116.342, 360.469, -615.055, -229.645, 58.34, -615.055, -66.6336, -21.1591, -615.055, 116.342, 252.61, -615.055, 116.342, -691.155, 615.055, 116.342, -270.779, 615.055, 30.9794, -294.928, 615.055, 116.342, -691.155, 615.055, 116.342, -691.155, 615.055, 109.679, -270.779, 615.055, 30.9794, -691.155, -615.055, 109.679, -294.928, -615.055, 116.342, -270.779, -615.055, 30.9794, -691.155, -615.055, 109.679, -691.155, -615.055, 116.342, -294.928, -615.055, 116.342, 360.469, -615.055, -229.645, 648.857, -615.055, 116.342, 648.857, -615.055, -426.104, 360.469, -615.055, -229.645, 252.61, -615.055, 116.342, 648.857, -615.055, 116.342, 360.469, 615.055, -229.645, 648.857, 615.055, 116.342, 252.61, 615.055, 116.342, 360.469, 615.055, -229.645, 648.857, 615.055, -426.104, 648.857, 615.055, 116.342, -294.928, 615.055, 116.342, 58.34, 615.055, -66.6336, -21.1591, 615.055, 116.342, -294.928, 615.055, 116.342, -270.779, 615.055, 30.9794, 58.34, 615.055, -66.6336, -21.1591, 615.055, 116.342, 360.469, 615.055, -229.645, 252.61, 615.055, 116.342, -21.1591, 615.055, 116.342, 58.34, 615.055, -66.6336, 360.469, 615.055, -229.645, 360.469, -440.566, -229.645, 648.857, 440.548, -426.104, 360.469, 440.548, -229.645, 360.469, -440.566, -229.645, 648.857, -440.566, -426.104, 648.857, 440.548, -426.104, 360.469, -440.566, -229.645, 58.34, 440.548, -66.6336, 58.34, -440.566, -66.6336, 360.469, -440.566, -229.645, 360.469, 440.548, -229.645, 58.34, 440.548, -66.6336, 58.34, -440.566, -66.6336, -270.779, 440.548, 30.9794, -270.779, -440.566, 30.9794, 58.34, -440.566, -66.6336, 58.34, 440.548, -66.6336, -270.779, 440.548, 30.9794, -691.155, 440.548, 109.679, -270.779, -440.566, 30.9794, -270.779, 440.548, 30.9794, -691.155, 440.548, 109.679, -691.155, -440.566, 109.679, -270.779, -440.566, 30.9794, -691.155, -440.566, 116.342, -691.155, 440.548, 109.679, -691.155, 440.548, 116.342, -691.155, -440.566, 116.342, -691.155, -440.566, 109.679, -691.155, 440.548, 109.679, -691.155, -440.566, 116.342, -294.928, 440.548, 116.342, -294.928, -440.566, 116.342, -691.155, -440.566, 116.342, -691.155, 440.548, 116.342, -294.928, 440.548, 116.342, -294.928, -440.566, 116.342, -21.1591, 440.548, 116.342, -21.1591, -440.566, 116.342, -294.928, -440.566, 116.342, -294.928, 440.548, 116.342, -21.1591, 440.548, 116.342, -21.1591, -440.566, 116.342, 252.61, 440.548, 116.342, 252.61, -440.566, 116.342, -21.1591, -440.566, 116.342, -21.1591, 440.548, 116.342, 252.61, 440.548, 116.342, 648.857, 440.548, 116.342, 252.61, -440.566, 116.342, 252.61, 440.548, 116.342, 648.857, 440.548, 116.342, 648.857, -440.566, 116.342, 252.61, -440.566, 116.342, 648.857, 440.548, -426.104, 648.857, -440.566, 116.342, 648.857, 440.548, 116.342, 648.857, 440.548, -426.104, 648.857, -440.566, -426.104, 648.857, -440.566, 116.342)

[node name="Ramp1" type="MeshInstance3D" groups=["VisibleGroup1"]]
transform = Transform3D(0.01, 0, 0, 0, 0, -0.01, 0, 0.01, 0, 0, 1.096, 0)
mesh = SubResource("ArrayMesh_ipegg")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_0x68l")
