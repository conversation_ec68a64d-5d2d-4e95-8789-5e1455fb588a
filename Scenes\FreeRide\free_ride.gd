class_name FreeRide
extends Node3D

@onready var player_parent = $Player
@onready var remotes_parent = $Remotes
@onready var lobby_connect_manager = $LobbyConnectManager
@onready var touch_controller = $HUD/TouchController
@onready var camera_controller = $CameraController
@onready var loading_map_parent = $HUD/LoadingMap
@onready var camera3d = $CameraController/SpringArm3D/Camera3D
@onready var vehicle_camera3d: Camera3D = $VehicleCamera3D
@onready var connecting_parent = $HUD/Connecting
@onready var progress_bar = $HUD/LoadingMap/ProgressBar
@onready var progress_label = $HUD/LoadingMap/ProgressBar/ProgressLabel
@onready var exit_popup = $HUD/ExitPopup
@onready var exit_button = $HUD/ExitButton
@onready var emote_touch = $HUD/EmoteTouch
@onready var warmth: TextureRect = $HUD/Warmth
@onready var hunger_show: Control = $HUD/HungerShow
@onready var text_hint_label = $HUD/LoadingMap/TextHint
@onready var text_hint_label2 = $HUD/Connecting/TextHint
@onready var loading_label = $HUD/LoadingMap/Label
@onready var loading_element = $HUD/LoadingMap/LoadingElement
@onready var hud = $HUD
@onready var pointer = $Pointer
@onready var exiting_panel = $ExitingPanel
@onready var transition_texture = $Transition
@onready var freeride_chat = $HUD/FreeRideChat
@onready var coin_adder_ui = $HUD/CoinAdderUI
@onready var map_parent = $MapParent
@onready var shader_label = $HUD/LoadingMap/ShaderLabel
@onready var music_selector:MusicSelector = $FreeRideMusicSelector
@onready var debug_control = $HUD/DebugControl
@onready var fps_label = $HUD/GridContainer/fps
@onready var ping_label = $HUD/GridContainer/Ping
@onready var settings_label = $HUD/GridContainer/GraphicSettings
@onready var active_label = $HUD/GridContainer/ActiveRemotes
@onready var mini_map = $HUD/MiniMap
@onready var client_reconnector = $ClientReconnector as ClientReconnector
@onready var player_counter_ui = $HUD/PlayerCounter
@onready var minimap_button = $HUD/MiniMapButton
@onready var job_panel: Control = $HUD/JobPanel
@onready var day_night_time_counter: TimeCounter = $DayNightTimeCounter
@onready var mobile_button: Control = $HUD/MobileButton
@onready var mobile_hud_parent: Node = $HUD/MobileHudParent
@onready var ringing: Panel = $HUD/MobileButton/Ringing
@onready var undercover: Label = $HUD/GridContainer/Undercover
@onready var mobile_image: TextureRect = $HUD/MobileButton/image
@onready var mobile_button_bg: TextureRect = $HUD/MobileButton/BGImage
@onready var player_inventory_container: PlayerInventoryContainer = $HUD/PlayerInventoryContainer
@onready var vehicle_touch: VehicleTouch = $HUD/VehicleTouch


var server: Server
var client: Client
var server_node = preload("res://networking/server.tscn")
var client_node = preload("res://networking/client.tscn")
enum CLIENT_STATE {Connecting, LoadingMap, Game}
var client_state = CLIENT_STATE.Connecting
var visible_manager: VisibleManager
var weapon_manager: WeaponManager
var policeOffice
var hospital

const MAP_PATH = "res://Scenes/FreeRide/Maps/AzadShahr.tscn"
#const MAP_PATH = "res://Scenes/FreeRide/Assets/TestFreeRide/TestFreeRideScene.tscn"

var map
var player: Character #My Player in Client
var text_hint_manager = TextHintManager.new()
var network_visibility_manager = NetworkVisibilityManager.new()
var character_change_listener_nodes = []

const GRAPHICS_VISIBILITY_MUL = [0.7, 1, 1, 1.2]
const GRAPHICS_SHADOW_ENABLED = [false, false, false, true]

func _ready():
	InventoryManager.decode(DataSaver.get_item("inventory"))
	exit_popup.visible = false
	randomize()
	mini_map.visible = false
	freeride_chat.visible = true
	visible_manager = VisibleManager.new()
	visible_manager.on_stand_changed.connect(apply_visible_stand)
	add_child(visible_manager)
	debug_control.visible = false
	debug_control.process_mode = Node.PROCESS_MODE_DISABLED
	#debug_control.visible = true

	transition_texture.visible = false
	add_child(network_visibility_manager)
	exiting_panel.visible = false
	current_camera()
	loading_label.text = tr("LOADING_MAP")
	loading_element.visible = true
	exit_popup.quit.connect(on_quit_pressed)
	server = server_node.instantiate()
	server.player_parent = player_parent
	add_child(server)
	server.game_scene = self
	Constants.server = server
	server.on_new_game()
	server_init()
	VehicleManager.set_server(server)
	
	client = client_node.instantiate()
	client.game_scene = self
	ClientRPC.client = client
	client.connect_success.connect(on_client_connect_success)
	client.error_connecting.connect(on_connection_error)
	client.disconnected.connect(on_disconnect)
	add_child(client)
	client.remote_parent = remotes_parent
	Constants.client = client
	client.server = server
	server.client = client
	lobby_connect_manager.client = client
	lobby_connect_manager.server = server
	client_init()
	Adivery.reparent_hud(self)
	MobileManager.reparent_hud(mobile_hud_parent)
	ClanManager.change_hud_parent(self)
	Selector.presence = Constants.PRESENCE_FREERIDE_MODE
	on_unmount_vehicle()


func _process(_delta):
	if quiting:
		return
	if Constants.is_client():
		var chat_vis = freeride_chat.chat_parent.visible
		if Constants.is_desktop():
			if Input.is_action_just_pressed("CycleDaylight"):
				change_day_night()
				
		#if Input.is_action_just_pressed("Chat") and not chat_vis:
		#	Constants.show_mouse()
		#	freeride_chat._on_expand_button_pressed()
		if Input.is_action_just_pressed("MiniMap") and not chat_vis:
			if not mini_map.visible:
				_on_mini_map_button_pressed()
			else:
				mini_map.exit()
		if Constants.is_desktop():
			if Input.is_action_just_pressed("mouse"):
				if Input.get_mouse_mode() == Input.MOUSE_MODE_VISIBLE:
					Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
				else:
					Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
		if Input.is_action_just_pressed("exit"):
			if Constants.is_desktop():
				_on_exit_button_pressed()
		
		if Input.is_action_just_pressed("shoot"):
			if Constants.is_desktop():
				player.start_shoot()
			player.item_action_start()
		
		if Input.is_action_just_pressed("Inv1"):
			InventoryManager.press_slot(0)
		if Input.is_action_just_pressed("Inv2"):
			InventoryManager.press_slot(1)
		if Input.is_action_just_pressed("Inv3"):
			InventoryManager.press_slot(2)
		if Input.is_action_just_pressed("Inv4"):
			InventoryManager.press_slot(3)
		if Input.is_action_just_pressed("Inv5"):
			InventoryManager.press_slot(4)
		
		ringing.visible = LiveKit.ringing
		mobile_image.visible = not LiveKit.ringing
		mobile_button_bg.visible = not LiveKit.ringing
		
			
		
		undercover.visible = Selector.undercover
		if not Constants.LOCAL_MODE:
			ping_label.text = "Ping " + str(client.ping_ms)
		if Selector.show_debug:
			fps_label.visible = true
			settings_label.visible = true
			active_label.visible = true
			fps_label.text = "FPS: " + str(Engine.get_frames_per_second())
			#settings_label.text = "Graphics: " + str(DataSaver.get_item("graphics_setting", 1))
			settings_label.text = "Prims: " + str(Performance.get_monitor(Performance.Monitor.RENDER_TOTAL_PRIMITIVES_IN_FRAME))
			active_label.text = "SyncV2: " + str(client.clientSyncV2.insight_player_count)  + " " + str(client.clientSyncV2.list_data_player_count)
		else:
			fps_label.visible = false
			settings_label.visible = false
			active_label.visible = false
		
		if is_instance_valid(player):
			if player.is_in_action_state:
				touch_controller.visible_action_button(true)
			else:
				touch_controller.visible_action_button(false)
		
			handle_player_action_client()
		else:
			handle_player_action_server()


###################################Server Code#################################
func server_init():
	if not Constants.is_server:
		return
	print("*******FreeRide Mode Server Start *****")

	if Constants.LOCAL_MODE:
		#select_local_mode_map()
		pass
	else:
		#Load Map
		var map_path = MAP_PATH
		print("Loading Map: ", map_path)
		var map_packed = load(map_path)
		map = map_packed.instantiate()
		map_parent.add_child(map)
		server.map_packed_scene = map_packed
		server.map = map
		network_visibility_manager.load_all_network_nodes()
		#client.network_visibility_manager = network_visibility_manager
		server.network_visibility_manager = network_visibility_manager
		print("Map Loadded and added")
		Constants.LOBBY_TIME = 1000 * 1000 * 1000
		server.set_state_to_lobby()


func handle_player_action_server():
	pass
###################################Server Code#################################


###################################Client Code#################################
func init_player_character():
	if Constants.is_headless:
		init_player_headless()
		return
	for ch in player_parent.get_children():
		ch.queue_free()

	var selected_player = load(Selector.selected_character["path"])
	player = selected_player.instantiate()
	player.name = "1"
	player_parent.add_child(player)
	player.clientNetworkHistory.reset()
	player.make_player()
	player.set_camera(camera_controller)
	player.set_controller(touch_controller)
	player.set_character_name(Selector.my_name)
	player.client = client
	player.server = server
	touch_controller.camera_controller = camera_controller
	touch_controller.client = client
	#player.debug = true


func init_player_headless():
	var selected_player = load(Selector.get_random_character_v2()["path"])
	print(selected_player.resource_path)
	player = selected_player.instantiate()
	player.name = str(multiplayer.get_unique_id())
	player_parent.add_child(player)
	player.clientNetworkHistory.reset()
	player.make_player()
	#player.set_camera(camera_controller)
	#player.set_controller(touch_controller)
	player.set_character_name("NPC")
	player.client = client
	player.server = server
	client.set_my_player_scene(player, selected_player.resource_path, Selector.selected_character["id"])
	#touch_controller.camera_controller = camera_controller
	#touch_controller.client = client


func client_init():
	if not Constants.is_client():
		return

	weapon_manager = WeaponManager.new()
	add_child(weapon_manager)
	music_selector.reset()
	SoundManager.stop_freeride_music()
	SoundManager.play_bg_music()
	shader_label.visible = false
	text_hint_manager.load_hints()
	client_state = CLIENT_STATE.Connecting
	change_hint_text()
	connect_to_server()
	client_reconnector.server = server
	client_reconnector.client = client


func connect_to_server():
	hideall_client_ui()
	connecting_parent.visible = true
	client_state = CLIENT_STATE.Connecting
	exit_button.visible = false
	init_player_character()
	ClientRPC.connect_to_server()


var thread: Thread
var client_map_resource
var map_using_cache = false
func client_load_map(resource):
	if Constants.is_headless:#ClientHeadless
		return
	multiplayer.rpc(1, Constants.server, "start_emote", [53])
	client_map_resource = resource
	hideall_client_ui()
	loading_map_parent.visible = true
	shader_label.visible = false
	client_state = CLIENT_STATE.LoadingMap
	await Constants.wait_frames(2)
	
	if GlobalShaderCache.freeride_scene == null:
		map_using_cache = false
		thread = Thread.new()
		thread.start(load_map_on_thread.bind())
	else:
		#print("Cache has Map: using cache")
		map_using_cache = true
		on_client_map_loaded()


func load_map_on_thread():
	if Constants.is_desktop():
		ResourceLoader.load_threaded_request(client_map_resource, "", false)
	else:
		ResourceLoader.load_threaded_request(client_map_resource, "", false)
	
	while(true):
		var process = []
		ResourceLoader.load_threaded_get_status(client_map_resource, process)
		var value = clamp(1, max(int(process[0] * 100), progress_bar.value), 99)
		call_deferred("update_progress", value)
		if process[0] == 1:
			await Constants.wait_frames(5)
			call_deferred("on_client_map_loaded")
			return
		OS.delay_msec(1)


func update_progress(value):
	var last_val = progress_bar.value
	progress_bar.value = value
	progress_label.text = str(value) + "%"
	if last_val != value:
		await Constants.wait_frames(1)


func on_client_map_loaded():
	text_hint_manager.queue_free()
	
	Selector.is_disconnected_from_game = false
	
	loading_label.text = tr("LOADING_FINISHED")
	loading_element.visible = false
	await Constants.wait_frames(5)
	
	
	if not map_using_cache:
		map = ResourceLoader.load_threaded_get(client_map_resource).instantiate()
		map.process_mode = PROCESS_MODE_DISABLED
		map_parent.add_child(map)
	else:
		progress_bar.visible = false
		progress_label.visible = false
		await Constants.wait_frames(1)
		map = GlobalShaderCache.freeride_scene
		map.reparent(map_parent)
		map.position.x = 0
	map.visible = true
	map.game_scene = self
	server.map = map
	map_parent.visible = false
	network_visibility_manager.load_all_network_nodes()
	client.network_visibility_manager = network_visibility_manager
	server.network_visibility_manager = network_visibility_manager
	character_change_listener_nodes = []
	for ch in get_tree().get_nodes_in_group("CharacterChanged"):
		character_change_listener_nodes.append(ch)
	
	if not map_using_cache:
		#await compile_map_shaders()
		map_parent.visible = true
		for ch in map.get_children():
			if "visible" in ch:
				ch.visible = true
		#Rotating Camera 360
		var start_angle = 0
		var frames = 4
		var angle_step = deg_to_rad(360.0 / frames)
		for i in range(0, frames):
			start_angle += angle_step
			camera_controller.rotation.y = start_angle
			await Constants.wait_frames(1)
	else:
		character_changed()
		map_parent.visible = true
		for ch in map.get_children():
			if "visible" in ch:
				ch.visible = true
		update_progress(100)
	
	camera_controller.set_start_player_camera_rotation()
	show_coins(3)
	
	var mul = GRAPHICS_VISIBILITY_MUL[DataSaver.get_item("graphics_setting", 1)]
	handle_world_enviroment()
	#mul = 0.01
	set_visibility_ranges(mul)
	client_start_game()
	music_selector.init()
	music_selector.start()
	visible_manager.init()
	visible_manager.start()
	GlobalShaderCache.freeride_scene = map
	multiplayer.rpc(1, Constants.server, "end_emote", [53])
	player.show_emote(53, 0.1)
	show_my_player_title()
	ViewportManager.set_scale3d()
	day_night_time_counter.start()
	policeOffice = map.policeOffice
	hospital = map.hospital
	map.process_mode = PROCESS_MODE_INHERIT
	client_state = CLIENT_STATE.Game
	ClientRPC.last_pong_time = -1
	InventoryManager.update_container()
	handle_player_on_map_load.call_deferred()


#Debug
func find_area_childs(parent):
	for ch in parent.get_children():
		find_area_childs(ch)
		if ch is Area3D:
			print(ch.name, " is area3d: ", parent, " ", parent.get_parent())


func client_start_game():
	if Constants.is_client() and not Constants.is_headless and Constants.is_desktop():
		Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	hideall_client_ui()
	exit_button.visible = true


func on_client_connect_success():
	if client_reconnector.is_start:
		client_reconnector.on_reconnected()
		return
	else:
		#First time
		client_reconnector.last_id = multiplayer.get_unique_id()
		client.set_my_player_scene(player, Selector.selected_character["path"], Selector.selected_character["id"])
		emote_touch.update_animations()

	client.init_after_connect()
	hideall_client_ui()
	loading_map_parent.visible = true


func on_connection_error():
	if quiting:
		return

	if client_reconnector.is_start:
		client_reconnector.on_connection_error()
		return
	if Selector.should_start_game_scene:
		Selector.should_start_game_scene = false
		get_tree().change_scene_to_file("res://Scenes/game_scene.tscn")
		return
	Selector.is_disconnected_from_game = true
	get_tree().change_scene_to_file("res://Scenes/FreeRide/freeride_server_selector.tscn")
	print("FreeRide Connection Error")


func on_disconnect():
	if quiting:
		return

	if Selector.should_start_game_scene:
		DataSaver.send_save_request()
		Selector.should_start_game_scene = false
		get_tree().change_scene_to_file("res://Scenes/game_scene.tscn")
		return
	
	client_reconnector.try_to_reconnect()


func set_visibility_ranges(mul=1):
	if Constants.is_server:
		return
	
	set_group_visibility_ranges("VisibleGroupInterior", 0, 25)
	set_group_visibility_ranges("VisibleGroup0", 0, 50 * mul)
	set_group_visibility_ranges("VisibleGroup1", 0, 100 * mul)
	set_group_visibility_ranges("VisibleGroup2", 0, 200 * mul)
	set_group_visibility_ranges("VisibleGroup3", 0, 300 * mul)
	set_group_visibility_ranges("VisibleParent1", 100 * mul, 0)
	set_group_visibility_ranges("VisibleParent2", 150 * mul, 0)
	set_group_visibility_ranges("VisibleParent3", 250 * mul, 0)


func set_group_visibility_ranges(group_name, v_begin, v_end):
	for child in get_tree().get_nodes_in_group(group_name):
		if not "visibility_range_begin" in child:
			print(child, " ", child.name, " : node is not GeometryInstance3d Type!! ", child.get_parent().name)
			continue
		
		child.visibility_range_begin = v_begin
		child.visibility_range_end = v_end


#Client
func hideall_client_ui():
	connecting_parent.visible = false
	loading_map_parent.visible = false


func _on_exit_button_pressed():
	MobileManager.hud.start_hide()
	exit_popup.popup()
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)


var quiting = false
#on exit_popup
func on_quit_pressed(to_server_select=false):
	if quiting:
		return

	SoundManager.stop_freeride_music()
	quiting = true
	client.connected = false
	ClientRPC.disconnect_from_server()
	await hideall_before_exit()
	if to_server_select:
		get_tree().change_scene_to_file("res://Scenes/FreeRide/freeride_server_selector.tscn")
	else:
		get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")


func hideall_before_exit():
	get_tree().call_group("OnExitFreeride", "on_exit_freeride")
	DataSaver.send_save_request()
	visible = true
	hud.visible = false
	exiting_panel.visible = true
	await Constants.wait_frames(2)

	player_parent.visible = false
	remotes_parent.visible = false
	if is_instance_valid(map):
		map.visible = false
		map.process_mode = PROCESS_MODE_DISABLED
		map.reparent(GlobalShaderCache)
		reconnect_nodes()#Try To exit and hide all UIs
		map.position.x = 1000 * 1000
	SoundManager.stop_freeride_music()
	return


func handle_player_action_client():
	if (Input.is_action_just_pressed("action") or touch_controller.action_pressed) and player.is_in_action_state:
		if player.actionable_scene != null:
			player.actionable_scene.on_action(player)


func character_changed():
	show_my_player_title()
	emote_touch.update_animations()
	for ch in character_change_listener_nodes:
		if ch.has_method("character_changed"):
			ch.character_changed()
	send_my_cosmetic_update()
###################################Client Code#################################


func change_hint_text():
	if Constants.is_server:
		return
	
	if not is_instance_valid(text_hint_manager):
		return

	text_hint_label.text = text_hint_manager.get_random_hint()
	text_hint_label2.text = text_hint_manager.get_random_hint()
	await Constants.wait_frames(3)
	await get_tree().create_timer(4).timeout
	if client_state == CLIENT_STATE.LoadingMap or client_state == CLIENT_STATE.Connecting:
		change_hint_text()


func _on_tree_exiting():
	SoundManager.stop_freeride_music()
	if thread:
		thread.wait_to_finish()
	Adivery.reparent_hud_to_self()
	MobileManager.reparent_hud_to_self()
	ClanManager.change_hud_parent_to_self()
	if is_instance_valid(text_hint_manager):
		text_hint_manager.queue_free()
	if is_instance_valid(visible_manager):
		visible_manager.queue_free()
	
	await NakamaManager.leave_freeride_chat()


func on_joined_minigame(hideController=true, hideEmoteTouch=true, hideChat=true, hideExit=true, hidePlayerCounter=true):
	if hideController:
		touch_controller.visible = false
	if hideEmoteTouch:
		emote_touch.visible = false
	if hideChat:
		freeride_chat.visible = false
	if hideExit:
		exit_button.visible = false
	if hidePlayerCounter:
		player_counter_ui.visible = false
	player_inventory_container.visible = false
	minimap_button.visible = false
	job_panel.visible = false
	touch_controller.arm_parent.visible = false
	mobile_button.visible = false
	warmth.visible = false
	hunger_show.visible = false
	vehicle_touch.exit_button.visible = false


func show_mobile_button():
	mobile_button.visible = true


func on_exit_minigame(reset_camera=true):
	mobile_button.visible = true
	exit_button.visible = true
	touch_controller.visible = true
	touch_controller.show_buttons()
	emote_touch.visible = true
	freeride_chat.visible = true
	player_counter_ui.visible = true
	minimap_button.visible = true
	player_inventory_container.visible = true
	current_camera()
	if reset_camera:
		camera_controller.reset_camera()
	job_panel.visible = true
	warmth.visible = true
	hunger_show.visible = true
	vehicle_touch.exit_button.visible = true


func on_mount_vehicle():
	touch_controller.visible = false
	vehicle_touch.visible = true
	VehicleManager.vehicle_touch = vehicle_touch
	player_inventory_container.visible = false


func on_unmount_vehicle():
	touch_controller.visible = true
	vehicle_touch.visible = false
	player_inventory_container.visible = true
	current_camera()


func current_camera():
	camera3d.make_current()


func show_coins(time=5):
	coin_adder_ui.show_for_secs(time)


func show_add_coins(time=1.5):
	coin_adder_ui.update(time)


func _on_debuggm_1_toggled(_button_pressed):
	pass
	#get_tree().root.use_occlusion_culling = button_pressed
	#print("Occlusion: ", button_pressed)


func set_map_process(start_index, end_index, button_pressed):
	var type = PROCESS_MODE_INHERIT
	if button_pressed == false:
		type = PROCESS_MODE_DISABLED

	print("Changing process type")
	for index in range(start_index, end_index):
		var ch = map.get_child(index)
		ch.process_mode = type
		print(index, ": ", ch.name)
	print("*********************")


func _on_debuggm_2_toggled(button_pressed):
	set_map_process(4, 6, button_pressed)


func _on_debugflowerbastan_toggled(button_pressed):
	set_map_process(6, 7, button_pressed)


func _on_debugbastan_toggled(button_pressed):
	set_map_process(7, 8, button_pressed)


func _on_debugmosque_toggled(button_pressed):
	map.grid_map_mosque.visible = button_pressed


func _on_debugflowerpersian_toggled(button_pressed):
	map.grid_map_flower_persian.visible = button_pressed


func _on_debug_disco_toggled(button_pressed):
	map.disco.change_light(button_pressed)


func _on_debugwater_toggled(button_pressed):
	map.water.visible = button_pressed


func apply_visible_stand(visible_stand):
	if map == null:
		return
	map.apply_visible_stand(visible_stand)


func _on_mini_map_button_pressed():
	mini_map.popup()


#Client
func compile_map_shaders():
	set_visibility_ranges(20)
	#Visible Step By Step
	update_progress(100)
	var total = 0
	for ch in map.get_children():
		if "visible" in ch:
			ch.visible = false
		total += 1
	map_parent.visible = true
	shader_label.visible = true

	total += 4 #Frames for camera rotation
	var done = 1
	for ch in map.get_children():
		if "visible" in ch:
			if ch.name.find("Light") == -1:
				ch.visible = true
		shader_label.text = tr("SHADER_LOADING") + " " + str(done) + "/" + str(total)
		done += 1
		if done % 2 == 0:
			await  Constants.wait_frames(1)
	#Loading Step By Step

	#Rotating Camera 360
	var start_angle = 0
	var frames = 2
	var angle_step = deg_to_rad(360.0 / frames)
	for i in range(0, frames):
		start_angle += angle_step
		camera_controller.rotation.y = start_angle
		shader_label.text = tr("SHADER_LOADING") + " " + str(done) + "/" + str(total)
		done += 1
		await Constants.wait_frames(1)
	#Rotating Camera 360


#depricated
func send_my_cosmetic_update():
	if quiting:
		return
	#print("cosmetics send: ", Constants.trim_cosmetics_data(player.cosmetic_data))
	#print("ignoring send cosmetics: ", len(Constants.trim_cosmetics_data(player.cosmetic_data)))
	return


func reconnect_nodes():
	ClientRPC.set_my_authoritive(false)
	for ch in get_tree().get_nodes_in_group("Reconnect"):
		if ch.has_method("reconnect"):
			ch.reconnect()
		else:
			print(ch.name, " not have reconnect method: ", ch.get_parent().name)
	
	get_tree().call_group("OnReconnect", "on_reconnect")
	#Reconnect success
	if Selector.in_prison or Selector.in_arresting:
		print("im in prison")
		player.im_alive()
		player.on_heal_finished()#Unfreeze
		ClientRPC.send_me_to_jail(policeOffice.prison.get_random_init_position())


func show_my_player_title():
	if Selector.my_title == null:
		return
	
	var should_sync = false
	if player.title_label.text != Selector.my_title:
		should_sync = true
	
	if DataSaver.get_item("arm", null) != null:
		should_sync = true

	if Selector.my_title.length() > 0:
		player.update_title_label(Selector.my_title, Selector.my_title_color)
		send_my_cosmetic_update()
	else:
		player.title_label.visible = false
		player.cosmetic_data["title"] = {
			"title": "",
			"color": "FFFFFF"
		}
		if should_sync:
			send_my_cosmetic_update()


func handle_world_enviroment():
	if map.environment == null:
		return
	
	var current_time = Time.get_datetime_dict_from_system()
	if current_time["hour"] < Selector.day_start_hour:
		set_env_night()
	elif current_time["hour"] < Selector.night_start_hour:
		set_env_day()
	else:
		set_env_night()


var its_day = true
func set_env_night():
	its_day = false
	if map.environment == null:
		return
	
	var shadow = GRAPHICS_SHADOW_ENABLED[DataSaver.get_item("graphics_setting", 1)]
	var env: Environment = map.environment.environment
	env.sky = preload("res://Scenes/FreeRide/Assets/NightSky.tres")
	env.ambient_light_color = Color("122655")
	
	if shadow == false:
		env.ambient_light_energy = 1.8
	else:
		env.ambient_light_energy = 0.7
	
	if map.has_method("set_shadow"):
		map.set_shadow(shadow)


func set_env_day():
	its_day = true
	if map.environment == null:
		return
	
	var shadow = GRAPHICS_SHADOW_ENABLED[DataSaver.get_item("graphics_setting", 1)]
	var env: Environment = map.environment.environment
	env.sky = preload("res://Scenes/FreeRide/Assets/DaySky.tres")
	env.ambient_light_color = Color("f0ab38")
	
	if shadow == false:
		env.ambient_light_energy = 2.5
	else:
		env.ambient_light_energy = 1
	
	if map.has_method("set_shadow"):
		map.set_shadow(shadow)


func change_day_night():
	if its_day:
		set_env_night()
	else:
		set_env_day()


#Check for prison and dead
func handle_player_on_map_load():
	if DataSaver.get_item("im_dead", false) == true:
		print("im dead at start")
		await Constants.wait_timer(1)
		player.im_dead()
	elif DataSaver.get_item("im_freezed", false) == true:
		print("im freezed at start")
		await Constants.wait_timer(1)
		player.freezeDeathState.start_state()
	if Selector.in_prison:
		print("im in prison")
		player.im_alive()
		player.on_heal_finished()#Unfreeze
		await Constants.wait_timer(1)
		ClientRPC.send_me_to_jail(policeOffice.prison.get_random_init_position())


func _notification(what: int) -> void:
	if Constants.is_server:
		return
	if what == NOTIFICATION_APPLICATION_RESUMED:
		print("resumed")
		if client_state == CLIENT_STATE.Game:
			if is_instance_valid(client_reconnector):
				client_reconnector.try_to_reconnect()


#Client
func _on_mobile_button_pressed() -> void:
	if get_mobile_in_hand():
		InventoryManager.swap_mobile_and_hand()


func get_mobile_in_hand() -> bool:
	if not MobileManager.i_has_mobile():
		Constants.show_toast(tr("NO_MOBILE"))
		return false
	
	if player.state in player.NO_MOBILE_STATES:
		return false
	
	MobileManager.hud.start_show()
	player.mobileState.start_state()
	
	return true


func reset_vehicle_camera():
	vehicle_camera3d.reset_camera()
