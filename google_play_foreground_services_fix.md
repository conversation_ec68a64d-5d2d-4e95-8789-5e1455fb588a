# Google Play Console Foreground Services Fix

## Problem
Google Play Console was raising an error about foreground services not being properly declared according to Android 14+ requirements.

## Root Cause
The Firebase Messaging Service was missing the required `android:foregroundServiceType` attribute and proper permissions for Android 14+ (API level 34).

## Solution Applied

### 1. Updated AndroidManifest.xml
**File:** `android/build/AndroidManifest.xml`

#### Added Required Permissions:
```xml
<!-- Foreground service permissions for Android 14+ -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

<!-- Internet and network permissions -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

#### Updated Firebase Messaging Service:
```xml
<service 
    android:directBootAware="true" 
    android:exported="false" 
    android:name="com.google.firebase.messaging.FirebaseMessagingService"
    android:foregroundServiceType="dataSync">
    
    <intent-filter android:priority="-500">
        <action android:name="com.google.firebase.MESSAGING_EVENT"/>
    </intent-filter>
</service>
```

### 2. Updated Export Presets
**File:** `export_presets.cfg`

Set target SDK to Android 14 (API level 34) for all export presets:
```
gradle_build/target_sdk="34"
```

## Key Changes Explained

1. **FOREGROUND_SERVICE Permission**: Required for any app that uses foreground services on Android 14+

2. **FOREGROUND_SERVICE_DATA_SYNC Permission**: Specific permission for services that sync data in the background

3. **FOREGROUND_SERVICE_SPECIAL_USE Permission**: Additional permission for special use cases

4. **POST_NOTIFICATIONS Permission**: Required for apps targeting Android 13+ that show notifications

5. **foregroundServiceType="dataSync"**: Declares that the Firebase service is used for data synchronization

6. **Target SDK 34**: Ensures compliance with latest Android requirements

## What This Fixes

- ✅ Google Play Console foreground services error
- ✅ Compliance with Android 14+ requirements
- ✅ Proper notification permissions
- ✅ Firebase Cloud Messaging compatibility
- ✅ Future-proofing for Play Store submissions

## Next Steps

1. **Rebuild your Android APK/AAB** with these changes
2. **Test notifications** to ensure Firebase messaging still works
3. **Upload to Google Play Console** - the error should be resolved
4. **Monitor for any new warnings** in future submissions

## Additional Notes

- These changes are backward compatible with older Android versions
- Firebase Cloud Messaging will continue to work as expected
- The app will now properly request notification permissions on Android 13+
- All export presets have been updated to target Android 14 (API 34)

## Testing Checklist

- [ ] App builds successfully
- [ ] Firebase notifications work
- [ ] No new Google Play Console errors
- [ ] App installs and runs on Android 14+ devices
- [ ] Notification permissions are properly requested
